import React, { useState, useEffect } from 'react';
import {
    Card,
    Checkbox,
    Select,
    Row,
    Col,
    Button,
    message,
    Spin,
    Alert,
    Tag,
    Radio,
    Form,
} from 'antd';
import moment from 'moment';
import http_utils from '../../../util/http_utils';
import './index.css';
import FormBuilder from 'antd-form-builder';
import TimePickerWidget from '../../wify-utils/TimePickerWidget';
import ConfigHelpers from '../../../util/ConfigHelpers';
import { convertDateFieldsToMoments } from '../../../util/helpers';
import BookingDayCards from './BookingDayCards';
import SelectedSlotSummary from './SelectedSlotSummary';
import { getServiceRequestDateMeta } from '../../../routes/services/helpers';

const { Option } = Select;

const MicroBookingSlot = ({
    savedSlots,
    allSrvcPrvdrs,
    formRef,
    initialValues,
    current_prvdr,
    editMode,
    srvcConfigData,
    vertical_id,
    onChange,
    canReschedule,
}) => {
    const forceUpdate = FormBuilder.useForceUpdate();
    const [selectedWeek, setSelectedWeek] = useState(null);
    const [weekOptions, setWeekOptions] = useState([]);
    const [weekData, setWeekData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [availableSlots, setAvailableSlots] = useState(undefined);
    const [selectedSlots, setSelectedSlots] = useState({});
    const [error, setError] = useState(null);
    const [capacityData, setCapacityData] = useState(null);
    const [selectedPrvdr, setSelectedPrvdr] = useState(null);

    const {
        srvc_possible_prvdrs: allowedSrvcPrvdrs,
        srvc_default_provider: default_srvc_prvdr,
        sbtsk_time_slot_lower_limit,
        sbtsk_time_slot_upper_limit,
    } = srvcConfigData || {};

    useEffect(() => {
        let shouldFetch = false;
        let fetchPrvdrId = null;

        const numericPrvdr = parseInt(current_prvdr, 10);

        // Handle current_prvdr updates
        if (current_prvdr && !isNaN(numericPrvdr)) {
            formRef.current.setFieldsValue({
                new_prvdr: numericPrvdr,
            });

            shouldFetch = true;
            fetchPrvdrId = numericPrvdr;
        }

        // Handle logged-in service provider case
        if (ConfigHelpers.isServiceProvider() && current_prvdr) {
            shouldFetch = true;
            fetchPrvdrId = numericPrvdr || current_prvdr;
        }

        // Handle savedSlots
        if (savedSlots) {
            setSelectedSlots(savedSlots);
            shouldFetch = true;
            fetchPrvdrId = numericPrvdr || current_prvdr;
        } else if (savedSlots === undefined && editMode) {
            setSelectedSlots({});
        }

        // Call fetchCapacityData only once
        if (shouldFetch && fetchPrvdrId) {
            fetchCapacityData(fetchPrvdrId);
        }

        // Generate week options
        generateWeekOptions();
    }, [current_prvdr, savedSlots, selectedPrvdr, editMode]);

    // Monitor changes to `cust_pincode` using `useEffect`
    useEffect(() => {
        console.log('Pincode changed');
        // Fetch available slots whenever the pincode changes
        const pincode = formRef?.current?.getFieldValue('cust_pincode');
        // debugger;
        if (
            pincode &&
            (selectedPrvdr || ConfigHelpers.isServiceProvider()) &&
            selectedWeek
        ) {
            fetchAvailableSlots();
        } else if (
            !pincode &&
            (selectedPrvdr || ConfigHelpers.isServiceProvider()) &&
            selectedWeek
        ) {
            setSelectedSlots({}); // if i am clearing the pincode after slot selection then remove slots as well
            formRef.current.setFieldsValue({
                booked_slots: undefined,
            });
            setAvailableSlots('pincode_missing');
        }
    }, [formRef?.current?.getFieldValue('cust_pincode')]);

    useEffect(() => {
        if (
            !editMode &&
            default_srvc_prvdr &&
            !formRef?.current?.getFieldValue('new_prvdr')
        ) {
            if (formRef) {
                formRef.current.setFieldsValue({
                    new_prvdr: default_srvc_prvdr,
                });
            }
            handlePrvdrSelector(default_srvc_prvdr);
        }
    }, [formRef]); //this should work on creation mode

    const generateWeekOptions = () => {
        const weeks = [];
        const today = moment().isValid() ? moment() : moment(new Date());

        // First week: today to upcoming Saturday
        const firstWeekEnd = today.clone().day(6); // 6 = Saturday
        if (firstWeekEnd.isBefore(today)) {
            firstWeekEnd.add(1, 'weeks'); // if today is Sunday, get next Saturday
        }

        weeks.push({
            value: `${today.format('YYYY-MM-DD')}_${firstWeekEnd.format('YYYY-MM-DD')}`,
            label: `(${today.format('ddd')}) ${today.format('Do MMM')} - (${firstWeekEnd.format('ddd')}) ${firstWeekEnd.format('Do MMM ')}`,
            startDate: today.format('YYYY-MM-DD'),
            endDate: firstWeekEnd.format('YYYY-MM-DD'),
        });

        // Generate next 4 weeks (Sun–Sat)
        const nextWeekStart = firstWeekEnd.clone().add(1, 'days'); // Sunday after first Saturday

        for (let i = 0; i < 4; i++) {
            const weekStart = nextWeekStart.clone().add(i * 7, 'days');
            const weekEnd = weekStart.clone().add(6, 'days');

            weeks.push({
                value: `${weekStart.format('YYYY-MM-DD')}_${weekEnd.format('YYYY-MM-DD')}`,
                label: `(${weekStart.format('ddd')}) ${weekStart.format('Do MMM')} - (${weekEnd.format('ddd')}) ${weekEnd.format('Do MMM ')}`,
                startDate: weekStart.format('YYYY-MM-DD'),
                endDate: weekEnd.format('YYYY-MM-DD'),
            });
        }

        setWeekOptions(weeks);
    };

    const refreshPage = () => {
        forceUpdate();
    };

    // Fetch org settings to get generated slots
    const fetchAvailableSlots = () => {
        if (loading) return;
        setLoading(true);
        setAvailableSlots(undefined);
        setError(undefined);
        let params = {
            org_id: formRef?.current?.getFieldValue('new_prvdr'),
            vertical_id: vertical_id,
            pincode: formRef?.current?.getFieldValue('cust_pincode'),
        };
        const onComplete = (resp) => {
            // console.log('Availability slots response:', resp);
            setAvailableSlots(resp.data);
            setLoading(false);
        };

        const onError = (error) => {
            //console.error('Error fetching availability slots:', error);
            setAvailableSlots(undefined);
            setError(http_utils.decodeErrorToMessage(error));
            setLoading(false);
        };

        http_utils.performGetCall(
            '/booking/availability-slots',
            params,
            onComplete,
            onError
        );
    };

    const fetchCapacityData = (prvdrId) => {
        if (loading) return;
        setLoading(true);
        setCapacityData(undefined);
        setError(undefined);
        let params = {
            srvc_prvdr_id: prvdrId,
        };

        const onComplete = (resp) => {
            // console.log('Availability slots response:', resp);
            setCapacityData(resp.data);
            setLoading(false);
        };

        const onError = (error) => {
            // console.error('Error fetching availability slots:', error);
            setCapacityData(undefined);
            setError(http_utils.decodeErrorToMessage(error));
            setLoading(false);
        };

        http_utils.performGetCall(
            '/booking/capacity',
            params,
            onComplete,
            onError
        );
    };

    // Fetch week data when week is selected
    const fetchWeekData = async (weekValue) => {
        const selectedWeekOption = weekOptions.find(
            (w) => w.value === weekValue
        );
        if (!selectedWeekOption) return;

        try {
            setLoading(true);

            // Generate days for the selected week
            const startDate = moment(selectedWeekOption.startDate);
            const endDate = moment(selectedWeekOption.endDate);

            // Validate moment objects
            if (!startDate.isValid() || !endDate.isValid()) {
                throw new Error('Invalid date format in week options');
            }
            const days = [];

            let currentDate = startDate.clone();
            while (currentDate.isSameOrBefore(endDate)) {
                days.push({
                    date: currentDate.format('YYYY-MM-DD'),
                    dayName: currentDate.format('dddd'),
                    displayDate: currentDate.format('Do MMM'),
                });
                currentDate.add(1, 'day');
            }

            setWeekData({
                ...selectedWeekOption,
                days: days,
            });

            // Only initialize empty slots if there are no existing selected slots
            // This prevents overriding saved slots in edit mode
            if (Object.keys(selectedSlots).length === 0) {
                const initialSlots = {};
                days.forEach((day) => {
                    initialSlots[day.date] = [];
                });
                setSelectedSlots(initialSlots);
            }
        } catch (error) {
            console.error('Error fetching week data:', error);
            message.error('Failed to fetch week data');
        } finally {
            setLoading(false);
        }
    };

    // Handle week selection
    const handleWeekSelect = (value) => {
        setSelectedWeek(value);
        fetchWeekData(value);
        fetchAvailableSlots();
    };

    // Handle slot selection for a specific day
    const handleSlotSelection = (date, slotValue) => {
        setSelectedSlots((prev) => {
            const daySlots = prev[date] || [];
            const isSelected = daySlots.includes(slotValue);

            const updatedSlots = {
                ...prev,
                [date]: isSelected
                    ? daySlots.filter((slot) => slot !== slotValue)
                    : [...daySlots, slotValue],
            };
            const selectedSlot = availableSlots?.slots?.find(
                (slot) => slot.label === slotValue
            );
            const capacityId = selectedSlot ? selectedSlot.capacity_id : null;

            // ✅ Filter out empty arrays
            const filteredSlots = Object.fromEntries(
                Object.entries(updatedSlots).filter(
                    ([_, slots]) => slots.length > 0
                )
            );

            // Make sure `onChange` is defined before calling it
            if (onChange) {
                onChange({
                    new_prvdr: formRef?.current?.getFieldValue('new_prvdr'),
                    booked_slots: filteredSlots,
                    capacity_id: capacityId,
                });
            }

            refreshPage();

            return updatedSlots;
        });
    };

    const handlePrvdrSelector = (value) => {
        setSelectedPrvdr(value);
        fetchCapacityData(value);
        //if user changes prvdr, reset selected slots in creation mode
        setSelectedSlots({});
        //also i can still see the error s if i change the prvdr
        setAvailableSlots(undefined);
        formRef.current.setFieldsValue({
            select_week: undefined,
        });
    };

    const getMeta = () => {
        const isPrvdr = ConfigHelpers.isServiceProvider();
        const isSrvcPrvdrSelected =
            formRef?.current?.getFieldValue('new_prvdr');
        const isCapcityEnabledFrSelectedPrvdr =
            capacityData?.enable_capacity_module;
        const prvdrNotEnabledCapacity =
            (isSrvcPrvdrSelected || isPrvdr) &&
            !isCapcityEnabledFrSelectedPrvdr;
        const isSlotsBooked = Object.values(selectedSlots).reduce(
            (total, daySlots) => total + daySlots.length,
            0
        );
        const startOfDay = sbtsk_time_slot_lower_limit || '05:00AM';
        const endOfDay = sbtsk_time_slot_upper_limit || '11:45PM';
        const startTimeFrEndTime = formRef?.current?.getFieldValue('start_time')
            ? formRef?.current?.getFieldValue('start_time')
            : initialValues?.start_time;
        const isSlotsAvailable = availableSlots?.slots?.length > 0;

        return {
            columns: 4,
            formItemLayout: null,
            // initialValues: initialValues,
            fields: [
                ...(!isPrvdr
                    ? [
                          {
                              key: 'new_prvdr',
                              label: 'Select Service Provider',
                              widget: 'select',
                              colSpan: 2,
                              options: allSrvcPrvdrs,
                              onChange: (value) => {
                                  handlePrvdrSelector(value);
                                  refreshPage();
                              },
                              disabled: editMode,
                          },
                      ]
                    : []),
                ...((isSrvcPrvdrSelected || isPrvdr || selectedPrvdr) &&
                isCapcityEnabledFrSelectedPrvdr &&
                !isSlotsBooked
                    ? [
                          {
                              key: 'select_week',
                              label: 'Select Week',
                              widget: 'select',
                              colSpan: 2,
                              options: weekOptions,
                              onChange: (value) => {
                                  handleWeekSelect(value);
                                  refreshPage();
                              },
                          },
                      ]
                    : []),
                ...(prvdrNotEnabledCapacity
                    ? [
                          ...getServiceRequestDateMeta(formRef).fields,
                          {
                              key: 'start_time',
                              label: 'Start Time',
                              widget: TimePickerWidget,
                              widgetProps: {
                                  beginLimit: startOfDay,
                                  endLimit: endOfDay,
                                  step: 15,
                              },
                              colSpan: 2,
                          },
                          {
                              key: 'end_time',
                              label: 'End Time',
                              widget: TimePickerWidget,
                              widgetProps: {
                                  beginLimit: startTimeFrEndTime
                                      ? startTimeFrEndTime
                                      : startOfDay,
                                  endLimit: endOfDay,
                                  step: 15,
                              },
                              colSpan: 2,
                          },
                      ]
                    : []),
                {
                    key: 'booked_slots',
                    className: 'gx-d-none',
                    widgetProps: {
                        hidden: true,
                    },
                },
                ...((isSrvcPrvdrSelected || isPrvdr) &&
                isCapcityEnabledFrSelectedPrvdr &&
                (isSlotsAvailable || isSlotsBooked)
                    ? [
                          {
                              key: 'booking_slots',
                              colSpan: 4,
                              render: () => {
                                  const hasSelectedSlots = Object.values(
                                      selectedSlots
                                  ).some((slots) => slots.length > 0);

                                  // ✅ Show only selected slot summary if slots are selected
                                  if (hasSelectedSlots) {
                                      return (
                                          <SelectedSlotSummary
                                              selectedSlots={selectedSlots}
                                              canReschedule={canReschedule}
                                              editMode={editMode}
                                              onClearSlots={() => {
                                                  // Clear selected slots or toggle to full booking card
                                                  setSelectedSlots({});
                                                  formRef.current.setFieldsValue(
                                                      {
                                                          booked_slots:
                                                              undefined,
                                                      }
                                                  );
                                                  formRef.current.setFieldsValue(
                                                      {
                                                          select_week: null,
                                                      }
                                                  );
                                                  // Reset week selection to allow new selection
                                                  setSelectedWeek(null);
                                                  setWeekData(null);
                                                  refreshPage();
                                              }}
                                          />
                                      );
                                  }

                                  // 🟩 Else show the full slot booking card
                                  return (
                                      <BookingDayCards
                                          weekData={weekData}
                                          selectedSlots={selectedSlots}
                                          availableSlots={availableSlots}
                                          handleSlotSelection={
                                              handleSlotSelection
                                          }
                                      />
                                  );
                              },
                          },
                      ]
                    : []),
            ],
        };
    };

    return (
        <div className="gx-mt-3">
            <FormBuilder
                meta={getMeta()}
                form={formRef}
                initialValues={initialValues}
            ></FormBuilder>
            {loading && (
                <div className="gx-text-center">
                    <div className="gx-loader">
                        <Spin />
                    </div>
                </div>
            )}

            {/* Show error below the selections if pincode is missing */}
            {availableSlots === 'pincode_missing' && (
                <div className="gx-text-red">
                    Please Enter Pincode to see time slots.
                </div>
            )}
            {availableSlots === 'no_slots_available_for_this_pincode' && (
                <div className="gx-text-red">
                    No slots available for this pincode.
                </div>
            )}
            {error && <p className="gx-text-red">{error}</p>}
        </div>
    );
};

export default MicroBookingSlot;
