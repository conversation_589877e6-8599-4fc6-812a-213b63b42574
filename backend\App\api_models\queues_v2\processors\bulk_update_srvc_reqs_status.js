const { allQueues } = require('../queues');
const { getBulkUpdateModelFrQueue } = require('./helpers/bulk_update_helper');
const {
    getBatchUpdateStatusEmailTemplate,
    getAllBatchProcessCompletedEmailTemplate,
} = require('./email/templates/batch_update_status_template');
/**
 * Queue processor for bulk updating service request statuses
 * Processes batches of service requests and updates their statuses directly
 */
const performJob = async (job, done) => {
    try {
        const app = require('../../../app');
        const {
            query,
            bulk_update_model_data,
            batchNumber,
            totalBatches,
            totalRecords,
        } = job.data;

        const { batch_data } = query;
        const user_email =
            bulk_update_model_data?.user_context?.user_details?.email;

        console.log(
            `bulk_update_srvc_req_status :: Processing batch ${batchNumber}/${totalBatches} with ${batch_data.length} records`
        );
        console.log(
            'bulk_update_srvc_req_status :: batch_data :: ',
            JSON.stringify(batch_data)
        );

        if (
            !batch_data ||
            !Array.isArray(batch_data) ||
            batch_data.length === 0
        ) {
            return done(
                new Error('Invalid batch_data. Expected non-empty array.')
            );
        }

        const bulkUpdateModel = getBulkUpdateModelFrQueue(
            app,
            bulk_update_model_data
        );

        const result =
            await bulkUpdateModel.processUpdateSrvcReqStatusBatch(query);

        if (!result.isSuccess()) {
            console.error(
                'bulk_update_srvc_req_status :: Error processing batch:',
                result.resp
            );
            throw new Error(result.resp || 'Failed to update batch');
        }

        console.log(
            'bulk_update_srvc_req_status :: Batch processed result :: ',
            result.resp
        );

        const resultData = JSON.parse(result.resp);
        // send failure email
        if (resultData && resultData.failure_results?.length > 0) {
            console.log(
                'bulk_update_srvc_req_status :: Failed to update following TMS IDs:',
                resultData.failure_results
            );
            const emailDetails = {
                batchNumber,
                totalBatches,
                batchSize: batch_data.length,
                failureResults: resultData.failure_results,
                successResults: resultData.success_results,
                emailSubject: `OWNER CONSOLE BATCH UPDATE FAILED ${batchNumber} of ${totalBatches}`,
            };
            sendBatchStatusEmail(emailDetails, user_email);
        } else {
            const emailDetails = {
                batchNumber,
                totalBatches,
                batchSize: batch_data.length,
                failureResults: resultData.failure_results,
                successResults: resultData.success_results,
                emailSubject: `OWNER CONSOLE BATCH UPDATE SUCCESS ${batchNumber} of ${totalBatches}`,
            };
            sendBatchStatusEmail(emailDetails, user_email);
        }

        // send final email
        if (batchNumber === totalBatches) {
            const emailNotification = {
                totalBatches,
                totalRecords,
            };
            sendBatchUpdateFinalEmail(emailNotification, user_email);
        }

        console.log(
            `bulk_update_srvc_req_status ::  batch ${batchNumber}/${totalBatches} completed`
        );

        done(null, { status: true });
    } catch (error) {
        console.error(
            'bulk_update_srvc_req_status :: Error in processor:',
            error
        );
        done(error);
    }
};

const sendBatchStatusEmail = (emailDetails, user_email) => {
    let message = getBatchUpdateStatusEmailTemplate(emailDetails);
    const emailJobData = {
        to: user_email,
        bcc: '<EMAIL>',
        subject: emailDetails?.emailSubject,
        message: message,
        attachments: '',
    };
    allQueues.WIFY_SEND_EMAIL.addJob(emailJobData);
};

const sendBatchUpdateFinalEmail = (emailDetails, user_email) => {
    let message = getAllBatchProcessCompletedEmailTemplate(emailDetails);
    const emailJobData = {
        to: user_email,
        bcc: '<EMAIL>',
        subject: 'OWNER CONSOLE SRVC REQS BATCH UPDATES COMPLETED',
        message: message,
        attachments: '',
    };
    allQueues.WIFY_SEND_EMAIL.addJob(emailJobData);
};

exports.default = performJob;
