import { <PERSON><PERSON>, Collapse, DatePicker, Spin } from 'antd';
import moment from 'moment';
import React from 'react';
import {
    decodeFieldsMetaFrmJson,
    decodeFileSectionsFrmJson,
} from '../../components/wify-utils/FieldCreator/helpers';
import RemoteSourceSelect from '../../components/wify-utils/RemoteSourceSelect';
import UserSelectorWidget from '../../components/wify-utils/UserSelectorWidget';
import ConfigHelpers from '../../util/ConfigHelpers';
import { getAddressFieldsMeta } from '../../util/CustomerHelpers';
import {
    getIsDeletedFrFilter,
    getNoOfTasksObjectFrFilter,
    getOptionsFrDefaultModeOfRating,
    getPresetRangesForRangeDatePicker,
    getPriorityObjectFrFilter,
    handleClearSelect,
    priorities,
} from '../../util/helpers';
import { filtersFrPrvdr } from './filters';
import { filters } from './filters';
import FormBuilder from 'antd-form-builder';
import TimePickerWidget from '../../components/wify-utils/TimePickerWidget';

//Get the date 5 months ago from the current date formatted as 'YYYY-MM-DD 00:00:00'
export const getAssignedToPrvdrStartDate = () => {
    return moment().subtract(5, 'months').format('YYYY-MM-DD 00:00:00');
};

//Get the current date formatted as 'YYYY-MM-DD 23:59:59'
export const getAssignedToPrvdrEndDate = () => {
    return moment().format('YYYY-MM-DD 23:59:59');
};

export const formatAddressWithoutPincode = (item) => {
    const { addr, cust_pincode } = item;
    // Return early if address is not provided
    if (!addr) {
        return addr;
    }
    // Split the addr by commas
    let addressParts = addr?.split(',');

    // Check if the pin code is present in the addressParts
    let pinCode = addressParts?.findIndex(
        (part) => part?.trim() !== cust_pincode?.trim()
    );
    if (!pinCode) {
        return addr;
    }
    // Remove pin code from addressParts array and trim
    addressParts = addressParts?.filter(
        (part) => part?.trim() !== cust_pincode
    );

    // Construct the formatted addr with pin code at the beginning
    let formattedAddress = `${addressParts?.map((part) => part?.trim()).join(', ')}`;
    return formattedAddress;
};

const tagsProto = {
    tags: [
        { value: 1, label: 'Apples' },
        { value: 2, label: 'Pears' },
    ],
    suggestions: [
        { value: 3, label: 'Auto built 1' },
        { value: 4, label: 'Auto built 2' },
        { value: 5, label: 'Auto built 3' },
        { value: 6, label: 'Auto built 4' },
    ],
};

export const getAuthorityLabelValuePairsWithPrefix = (authoritiesList) => {
    let labelValuePairData = [];
    if (authoritiesList && authoritiesList.length > 0) {
        authoritiesList.map((singleAuthority) => {
            let authority_key_ = 'authority_' + singleAuthority.value;
            labelValuePairData.push({
                label: singleAuthority.label,
                value: authority_key_,
                key: authority_key_,
            });
        });
    }
    return labelValuePairData;
};

export const getCalculatedTask = () => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'mandays',
                label: 'Mandays',
            },
            {
                key: 'sp_work_progress',
                label: 'SP Work progress percentage',
            },
        ],
    };
    return meta;
};

export const getSpCustomFileMetaFrmConfig = (srvcConfigData) => {
    let customFileFields = decodeFileSectionsFrmJson(
        srvcConfigData?.sp_cust_fields_json
    );
    if (customFileFields && customFileFields.length > 0) {
        customFileFields.forEach((singleCustFileField) => {
            singleCustFileField['label'] = singleCustFileField.title;
        });
    }
    const meta = {
        columns: 2,
        formItemLayout: null,
        fields: customFileFields.length > 0 ? [...customFileFields] : [],
    };
    return meta;
};

export const getSpCustomFileMetaFrmConfigFrDailyUpdate = (srvcConfigData) => {
    let customFileFields = decodeFileSectionsFrmJson(srvcConfigData);
    if (customFileFields && customFileFields.length > 0) {
        customFileFields.forEach((singleCustFileField) => {
            singleCustFileField['label'] = singleCustFileField.title;
        });
    }
    const meta = {
        columns: 2,
        formItemLayout: null,
        fields: customFileFields.length > 0 ? [...customFileFields] : [],
    };
    return meta;
};

export const getLineItem = () => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'sp_total_quantity',
                label: 'SP Total quantity',
            },
            {
                key: 'sp_total_price',
                label: 'SP Total price',
            },
        ],
    };
    return meta;
};

export const getChangeLockMeta = (isCustAcces) => {
    let changeLockFields = [];
    if (!ConfigHelpers.isServiceProvider() || isCustAcces) {
        changeLockFields.push({
            key: 'locked_for_change',
            label: 'Locked for change (Yes/No)',
        });
    } else {
        changeLockFields.push(
            {
                key: 'sp_locked_for_change',
                label: 'SP Locked for change (Yes/No)',
            },
            {
                key: 'sp_locked_for_change_by',
                label: 'SP Locked for change by',
            },
            {
                key: 'sp_locking_date',
                label: 'SP Locking Date',
            }
        );
    }
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [...changeLockFields],
    };
    return meta;
};

export const getPiInfo = () => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'final_amount',
                label: 'Final amount',
            },
            {
                key: 'sp_sent_for_billing',
                label: 'SP sent for billing',
            },
        ],
    };
    return meta;
};

export const getModifiedFieldsFrPayout = (spPayoutConfig) => {
    let resultArray = [
        {
            key: 'total_quantity',
            label: 'Total quantity',
        },
        {
            key: 'total_price',
            label: 'Total price',
        },
        {
            key: 'total_payout',
            label: 'Total',
        },
    ];
    if (spPayoutConfig) {
        const nestedObject = spPayoutConfig;
        resultArray = Object.values(nestedObject).flatMap((item) => [
            {
                key: 'total_quantity',
                label: item.quantity_field_label || 'Total quantity',
            },
            {
                key: 'total_price',
                label: item.price_field_label || 'Total price',
            },
            {
                key: 'total_payout',
                label: item.total_field_label || 'Total',
            },
        ]);
        return resultArray;
    }
    return resultArray;
};

export const getPayoutInfo = (srvc_type_sp_payouts_config) => {
    const spPayoutConfig = srvc_type_sp_payouts_config
        ? JSON.parse(srvc_type_sp_payouts_config)
        : undefined;
    let modifiedFields = getModifiedFieldsFrPayout(spPayoutConfig);
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [...modifiedFields],
    };
    return meta;
};

export const addDefaultKeysForTabularView = () => {
    let defaultKeys = [];
    getDefaultColumnsForTabularView().forEach((singleColumn) => {
        defaultKeys.push(singleColumn.key);
    });
    return defaultKeys;
};
export const getGeneralFileSection = () => {
    return [
        {
            key: 'general',
            label: 'Attachments',
        },
    ];
};

export const getDefaultColumnsForTabularView = () => {
    let defaultColumnsFrTabularView = [];
    let tabularViewDefaultColumn = [];
    let getStaticFieldsMeta = [
        ...getCustomerInfoMeta().fields,
        ...getAddressInfoMeta().fields,
        ...getRequestInfoMeta().fields,
    ];
    if (ConfigHelpers.isServiceProvider()) {
        tabularViewDefaultColumn = [
            { key: 'srvc_prvdr', label: 'Service Provider' },
            { key: 'request_priority' },
            { key: 'request_description' },
            { key: 'cust_full_name' },
            { key: 'creation_date' },
            { key: 'request_req_date' },
            { key: 'full_address', label: 'Address' },
            { key: 'sbtsks', label: 'Subtasks' },
            { key: 'attachments', label: 'Attachments' },
        ];
    } else {
        tabularViewDefaultColumn = [
            { key: 'srvc_prvdr', label: 'Service Provider' },
            { key: 'request_priority' },
            { key: 'request_description' },
            { key: 'cust_full_name' },
            { key: 'creation_date' },
            { key: 'request_req_date' },
            { key: 'full_address', label: 'Address' },
            { key: 'sbtsks', label: 'Subtasks' },
            { key: 'attachments', label: 'Attachments' },
            { key: 'status_transition_date', label: 'Transition Date' },
        ];
    }

    tabularViewDefaultColumn.forEach((singleDefaultField) => {
        let filteredDefaultField = getStaticFieldsMeta.filter(
            (singleStaticField) =>
                singleStaticField.key == singleDefaultField.key
        )?.[0];
        filteredDefaultField
            ? defaultColumnsFrTabularView.push(filteredDefaultField)
            : defaultColumnsFrTabularView.push(singleDefaultField);
    });

    return defaultColumnsFrTabularView;
};

export const getIsTabularView = (tabularViewPreferenceKey) => {
    let isTabularView = false;
    let tabularViewExisingData = localStorage.getItem('isTabularView');
    if (tabularViewExisingData) {
        tabularViewExisingData = JSON.parse(tabularViewExisingData);
        if (tabularViewExisingData[tabularViewPreferenceKey] == true) {
            isTabularView = true;
        }
    }
    return isTabularView;
};

export const getSrvcReqStaticAndCustomPossibleFields = (
    customFields = [],
    customeFieldsToRemove = [],
    attachmentDefaultSection = false,
    possibleAuthorities = []
) => {
    let PossibleFieldsFrTabularView = [];
    let removeKeyFromAddressMeta = [
        'address',
        'location',
        'clear_fields',
        'request_cc_users',
        'request_labels',
    ];
    if (customeFieldsToRemove.length > 0) {
        removeKeyFromAddressMeta.push(...customeFieldsToRemove);
    }
    let getFieldsMetaFrTabularView = [
        ...getCustomerInfoMeta().fields,
        ...getAddressInfoMeta().fields,
        ...(attachmentDefaultSection
            ? getGeneralFileSection()
            : getDefaultColumnsForTabularView()),
        ,
        ...customFields,
        ...getRequestInfoMeta().fields,
        ...possibleAuthorities,
    ];

    //remove dublicate object
    let fieldsMetaFrTabularView = getFieldsMetaFrTabularView.filter(
        (singleField, index, self) => {
            return (
                index === self.findIndex((obj) => obj?.key === singleField?.key)
            );
        }
    );

    fieldsMetaFrTabularView.forEach((singleField) => {
        if (
            singleField.label &&
            !(
                removeKeyFromAddressMeta.includes(singleField.key) ||
                customeFieldsToRemove.includes(singleField.key)
            )
        ) {
            let dummyObj = {
                value: singleField.key,
                label: singleField.label,
            };
            PossibleFieldsFrTabularView.push(dummyObj);
        }
    });
    return PossibleFieldsFrTabularView;
};

export const getCustomFieldsFrmConfig = (srvcConfigData) => {
    let customFields = decodeFieldsMetaFrmJson(
        srvcConfigData?.srvc_cust_fields_json
    );
    const meta = {
        columns: 2,
        formItemLayout: null,
        fields: customFields.length > 0 ? [...customFields] : [],
    };
    return meta;
};

export const getVerticalsFrmConfig = (srvcConfigData) => {
    let customFields = decodeFieldsMetaFrmJson(
        srvcConfigData?.sp_cust_fields_json
    );
    const meta = {
        columns: 2,
        formItemLayout: null,
        fields: customFields.length > 0 ? [...customFields] : [],
    };
    return meta;
};
export const getPayoutInfoFrmConfig = (srvcConfigData) => {
    let customFields = decodeFieldsMetaFrmJson(
        srvcConfigData?.srvc_type_sp_payouts_config
    );
    const meta = {
        columns: 2,
        formItemLayout: null,
        fields: customFields.length > 0 ? [...customFields] : [],
    };
    return meta;
};

export const getDailyUpdateFrmConfig = (srvcConfigData) => {
    let customFields = decodeFieldsMetaFrmJson(
        srvcConfigData?.sp_daily_update_form_fields
    );
    const meta = {
        columns: 2,
        formItemLayout: null,
        fields: customFields.length > 0 ? [...customFields] : [],
    };
    return meta;
};

export const getIssuesFrmConfig = (srvcConfigData) => {
    let customFields = decodeFieldsMetaFrmJson(
        srvcConfigData?.daily_update_issue_form_fields
    );
    const meta = {
        columns: 2,
        formItemLayout: null,
        fields: customFields.length > 0 ? [...customFields] : [],
    };
    return meta;
};

export const getSPAuthorityFrmConfig = (sp_authority_data, vertical_data) => {
    if (vertical_data.authority_id) {
        let spAuthorities = [];
        if (sp_authority_data.length > 0) {
            sp_authority_data.forEach((item) => {
                if (vertical_data.authority_id.includes(item.value)) {
                    spAuthorities.push({
                        label: item.label,
                        key: 'authority_' + item.value,
                    });
                }
            });
        }
        const meta = {
            columns: 2,
            formItemLayout: null,
            fields: spAuthorities.length > 0 ? [...spAuthorities] : [],
        };
        return meta;
    }
    return [];
};

export const getBrandAuthorityFrmConfig = (
    brand_authority_data,
    srvc_authorities_role_ids
) => {
    if (srvc_authorities_role_ids?.length > 0) {
        let brandAuthoritiesFields = [];
        if (brand_authority_data?.length > 0) {
            brand_authority_data.forEach((item) => {
                if (srvc_authorities_role_ids.includes(item.value)) {
                    brandAuthoritiesFields.push({
                        label: item.label,
                        key: 'authority_' + item.value,
                    });
                }
            });
        }
        const meta = {
            columns: 2,
            formItemLayout: null,
            fields: brandAuthoritiesFields,
        };
        return meta;
    }
    return [];
};

export const getSpCustomFieldsFrmConfig = (spConfigData) => {
    let customFields = [];
    if (spConfigData?.settings_data) {
        customFields = decodeFieldsMetaFrmJson(
            spConfigData?.settings_data?.sp_cust_fields_json
        );
    }
    const meta = {
        columns: 2,
        formItemLayout: null,
        fields: customFields.length > 0 ? [...customFields] : [],
    };
    return meta;
};

export const getFeedbackFieldsFrmConfig = (srvcConfigData) => {
    let feedbackFields = decodeFieldsMetaFrmJson(
        srvcConfigData?.srvc_rate_fields_json
    );
    const meta = {
        columns: 2,
        formItemLayout: null,
        fields:
            feedbackFields.length > 0
                ? [
                      {
                          key: 'feedback_received',
                          label: 'Feedback Received (Yes/No)',
                      },
                      ...feedbackFields,
                  ]
                : [],
    };
    // console.log("meta",meta);
    return meta;
};

export const getRequestInfoMeta = (srvcDetails) => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'request_description',
                colSpan: 4,
                label: 'Description',
                widget: 'textarea',
                required: true,
            },
            {
                key: 'request_req_date',
                label: 'Req. Service Date',
                colSpan: 2,
                widgetProps: {
                    style: {
                        width: '100%',
                    },
                },
                widget: 'date-picker',
            },

            {
                key: 'request_priority',
                label: 'Priority',
                colSpan: 2,
                widget: 'select',
                options: priorities, // to be loaded from API
                required: true,
                widgetProps: {
                    onChange: (value) => {
                        // console.log('Priority - ',value);
                    },
                },
            },
            {
                key: 'request_labels',
                label: 'Labels',
                colSpan: 2,
                widget: RemoteSourceSelect,
                options: tagsProto.suggestions,
                renderView: (values) => (
                    <div>
                        {typeof values.map == 'function' &&
                            values.map((value) => value.label + ',')}
                    </div>
                ),
                widgetProps: {
                    mode: 'tags',
                    url: '/searcher',
                    placeholder: 'Start typing..',
                    params: {
                        srvc_type_id: srvcDetails?.srvc_id,
                        fn: 'getServiceRequestLabels',
                    },
                    widgetProps: {
                        mode: 'tags',
                        style: {
                            width: '100%',
                        },
                    },
                    onChange: (value) => {
                        // formRef?.current.setFieldsValue({
                        //     request_labels: value
                        // })
                    },
                },
            },
            {
                key: 'request_cc_users',
                label: 'CC users',
                widget: UserSelectorWidget,
                widgetProps: {
                    mode: 'multiple',
                    // onChange : value => {
                    //     console.log('CC users value - ',value);
                    // }
                },
                renderView: (values) => (
                    <div>
                        {typeof values.map == 'function' &&
                            values.map((value) => value.label + ',')}
                    </div>
                ),
                colSpan: 2,
            },
            {
                key: 'creation_date',
                label: 'Creation Date',
                colSpan: 2,
                tooltip:
                    'You can specify a past creation date, incase you are creating entry on a different day. You can ignore this if you are creating the request on same day',
                widgetProps: {
                    disabledDate: (current) =>
                        current && current > moment().endOf('day'),
                    style: {
                        width: '100%',
                    },
                },
                widget: 'date-picker',
            },
            ...(ConfigHelpers.isServiceProvider()
                ? [
                      {
                          key: 'vertical_title',
                          colSpan: 4,
                          label: 'Request vertical',
                          widget: 'textarea',
                      },
                  ]
                : []),
        ],
    };
    return meta;
};
export const getAddressInfoMeta = (formRef) => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'address',
                colSpan: 4,
                render() {
                    return (
                        <fieldset>
                            <legend>
                                <b>Address</b>
                            </legend>
                        </fieldset>
                    );
                },
            },
            ...getAddressFieldsMeta(formRef),
        ],
    };
    return meta;
};

export const getOrganisationinfoMeta = () => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'brand_name',
                label: 'Brand Name',
            },
            {
                key: 'service_type',
                label: 'Service Type',
            },
            {
                key: 'vertical_fr_req_dump',
                label: 'Vertical',
            },
        ],
    };
    return meta;
};

export const getOrganisationinfoMetaFrProjects = () => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'brand_name',
                label: 'Brand Name',
            },
            {
                key: 'service_type',
                label: 'Service Type',
            },
            {
                key: 'location_group',
                label: 'Site location group',
            },
        ],
    };
    return meta;
};

export const getOnFieldTaskInfoMeta = () => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'assignee_name',
                label: 'Assignee name',
            },
            {
                key: 'task_status',
                label: 'Task status',
            },
            {
                key: 'task_start_date',
                label: 'Task start date',
                widget: 'date-picker',
            },
            {
                key: 'assigned_by',
                label: 'Assigned by',
            },
            {
                key: 'assigned_date',
                label: 'Assigned date',
            },
            {
                key: 'assigned_time',
                label: 'Assigned time',
            },
            {
                key: 'task_status_remark',
                label: 'Task status remark',
            },
            {
                key: 'task_geo_verification_status',
                label: 'GEO verification status',
            },
        ],
    };
    return meta;
};

export const getTransitionOverviewMeta = (possibleStatus) => {
    let possibleStatusFields = [];
    if (possibleStatus) {
        possibleStatus.map((singlePossibleStatus) => {
            let possibleStatusObj = {
                key: singlePossibleStatus.value,
                label: singlePossibleStatus.title,
            };
            possibleStatusFields.push(possibleStatusObj);
        });
    }
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [...possibleStatusFields],
    };
    return meta;
};

export const getCustomerInfoMeta = (
    formRef,
    state,
    handleCustMobileChanged,
    clearCustomerDetailsInForm
) => {
    let enteredMobileNumber = formRef?.current?.getFieldValue('cust_mobile');
    if (enteredMobileNumber && enteredMobileNumber.length != 10) {
        enteredMobileNumber = false;
    }
    const customerInfoFields = [
        {
            key: 'cust_mobile',
            label: 'Mobile(+91)',
            required: true,
            onChange: (e) => {
                if (!state?.editMode) handleCustMobileChanged(e.target.value);
            },
            widgetProps: {
                suffix: state?.isLoadingCustomerDetails ? (
                    <Spin size="small" className="gx-m-0" />
                ) : (
                    ''
                ),
            },
            extra: state?.autoFillCustId ? (
                <span className="gx-text-grey">(Existing customer)</span>
            ) : enteredMobileNumber &&
              !state?.editMode &&
              !state?.isLoadingCustomerDetails ? (
                <span>
                    (New customer){' '}
                    <a onClick={(e) => clearCustomerDetailsInForm()}>
                        Reset/Clear fields
                    </a>
                </span>
            ) : (
                ''
            ),
            rules: [
                {
                    pattern: new RegExp('^[0-9]*$'),
                    message: 'Incorrect number',
                },
                { min: 10 },
                { max: 10 },
            ],
        },
        {
            key: 'cust_full_name',
            label: 'Name',
            required: true,
            rules: [
                {
                    max: 100,
                },
            ],
        },
        {
            key: 'cust_email',
            label: 'Email',
            rules: [
                {
                    type: 'email',
                    max: 100,
                },
            ],
        },
    ];
    const meta = {
        formItemLayout: null,
        fields: [
            {
                key: 'customer_info',
                colSpan: 4,
                render() {
                    return (
                        <fieldset>
                            <legend>
                                <b>Customer information</b>
                            </legend>
                        </fieldset>
                    );
                },
            },
            ...customerInfoFields,
        ],
    };
    return meta;
};

export const getAllMergedAttachments = (
    sbtskDetailViewData,
    prevSelectedFiles
) => {
    let allFiles = [];
    if (sbtskDetailViewData) {
        sbtskDetailViewData.forEach((singleassginee) => {
            if (singleassginee.form_data) {
                singleassginee.form_data.forEach((singleAssigneeFormData) => {
                    let filetypeKeys = Object.keys(singleAssigneeFormData);
                    filetypeKeys.forEach((singleFileTypeKey) => {
                        let attachment = [];
                        if (singleAssigneeFormData?.[singleFileTypeKey]) {
                            Object.keys(
                                singleAssigneeFormData?.[singleFileTypeKey]
                            ).forEach((singleFile) => {
                                attachment.push(
                                    ...singleAssigneeFormData?.[
                                        singleFileTypeKey
                                    ]?.[singleFile]
                                );
                            });
                        }
                        attachment = removeCommonElements(
                            prevSelectedFiles,
                            attachment
                        );
                        if (attachment.length > 0) {
                            allFiles.push(...attachment);
                        }
                    });
                });
            }
        });
    }
    return allFiles; //filsForPreview
};

export const showExtOrderId = (config_data) => {
    let order_id = false;
    if (config_data?.show_ext_order_id_on_the_subtask) {
        order_id = true;
    }
    return order_id;
};

export const getSrvcReqCalendarData = (calendarDataResp, is_srvc_prvdr) => {
    let org_type = is_srvc_prvdr ? 'ORG_TYPE_SRVC_PRVDR' : 'ORG_TYPE_CUSTOMER';
    let calendar_data = [];
    if (calendarDataResp) {
        calendarDataResp.calendar_data.forEach((singleCalendarDataResp) => {
            if (org_type == singleCalendarDataResp.org_type) {
                calendar_data = singleCalendarDataResp.srvc_req_calendar_data;
            }
        });
    }
    return calendar_data;
};

export const getSrvcReqStaticAndCustomPossibleFieldsFilter = (
    customFields,
    authorityList
) => {
    let spAuthorities = [];
    if (authorityList.length > 0) {
        authorityList.forEach((item) => {
            spAuthorities.push({
                label: 'SP ' + item.label,
                key: 'authority_' + item.value,
            });
        });
    }
    let feedbackReceived = {
        key: 'feedback_received',
        label: 'Feedback Received (Yes/No)',
    };

    const options = [
        ...customFields,
        ...filtersFrPrvdr.filter((item) => item.key !== 'verticals_list'), // Exclude verticals_list option in dropdown
        feedbackReceived,
        getNoOfTasksObjectFrFilter(),
        getPriorityObjectFrFilter(),
        getIsDeletedFrFilter(),
        ...spAuthorities,
    ].map((item) => ({
        label: item.label,
        value: item.key,
    }));
    return options;
};

const getStatusTransitionFilters = (statuses) => {
    if (statuses.length > 0) {
        const statusTransitionDateFilters = statuses.map((status) => {
            return {
                key: `${status.key}_transition_date`,
                label: `${status.title} transition date`,
                widget: DatePicker.RangePicker,
                widgetProps: {
                    ranges: getPresetRangesForRangeDatePicker(),
                    disabledDate: (current) =>
                        current && current > moment().endOf('day'),
                },
            };
        });
        return statusTransitionDateFilters;
    } else {
        return [];
    }
};

export const getStaticAndCustomFieldsFilter = (
    custom_fields,
    authoritiesList,
    srvcStatuses,
    isPrvdr = false
) => {
    let authoritiesFilters = [];
    let possibleFilters = [];
    if (authoritiesList?.length > 0) {
        authoritiesList.forEach((singleAuthoritiesFilters) => {
            let filterObj = {
                key: 'authority_' + singleAuthoritiesFilters?.value?.toString(),
                label: singleAuthoritiesFilters.label,
            };
            authoritiesFilters.push(filterObj);
        });
    }
    let feedbackReceived = {
        key: 'feedback_received',
        label: 'Feedback Received (Yes/No)',
    };

    let feedbackstar = {
        key: 'feedback_star',
        label: 'Feedback Star',
    };
    let getFitlerMeta = [
        ...custom_fields,
        ...(isPrvdr ? filtersFrPrvdr : filters),
        getPriorityObjectFrFilter(),
        getNoOfTasksObjectFrFilter(),
        getIsDeletedFrFilter(),
        ...authoritiesFilters,
        feedbackReceived,
        isPrvdr ? null : feedbackstar,
        ...getStatusTransitionFilters(srvcStatuses),
    ];
    getFitlerMeta.forEach((singleField) => {
        if (singleField.label) {
            let dummyObj = {
                value: singleField.key,
                label: singleField.label,
            };
            possibleFilters.push(dummyObj);
        }
    });
    return possibleFilters;
};
export const RATING_TYPE = {
    authority: [
        { value: 'another_authority', label: 'Another Authority' },
        { value: 'static_user', label: 'Static User' },
    ],
    assignee: [
        { value: 'authority', label: 'Authority' },
        { value: 'static_user', label: 'Static User' },
    ],
};

export const getRatingFormMeta = (initialValues, params) => {
    const meta = {
        formItemLayout: null,
        initialValues: initialValues,
        fields: [
            {
                name: 'alert',
                label: 'Alert',
                render: () => <Alert message={params.message} type="info" />,
            },
            ...getMetaFrRatingAssigneesOrAuthorities(params)?.fields,
        ],
    };
    return meta;
};
export const getAssigneeMetaFrAssigneeOnlyTaskBased = (params) => {
    const { priorities, tabName, isProjectBased, viewData, refresh, formRef } =
        params;

    return {
        formItemLayout: 'vertical',
        fields:
            !isProjectBased && tabName == 'assignee'
                ? [
                      {
                          key: 'selected_roles_to_be_rated',
                          label: 'Select roles to be rated',
                          colSpan: 2,
                          widget: 'select',
                          options: viewData?.role_list || [],
                          widgetProps: {
                              mode: 'multiple',
                              allowClear: true,
                              showSearch: true,
                              optionFilterProp: 'children',
                              onChange: (value) => {
                                  console.log('selected value', value);
                                  refresh();
                              },
                          },
                      },
                      {
                          label: <span>Select default mode of rating</span>,
                          key: 'mode_of_rating_fr_assignees',
                          widget: 'radio-group',
                          onChange: () => {
                              refresh();
                          },
                          widgetProps: {
                              defaultValue: 'task_wise',
                          },
                          options: getOptionsFrDefaultModeOfRating(),
                      },
                  ]
                : [],
    };
};

export const getMetaFrRatingAssigneesOrAuthorities = (params) => {
    const {
        viewData,
        formRef,
        editMode,
        keySelectedAuthoritiesorDeployment,
        keyRoleList,
        collapseKey,
        tabName,
        isProjectBased,
        selectedRolesToBeRatedKey,
    } = params;
    let ratingAssigneesOrAuthorities = [];
    let selectedAuthoritiesorDeployment;
    if (tabName == 'authority') {
        selectedAuthoritiesorDeployment = formRef?.current?.getFieldValue(
            keySelectedAuthoritiesorDeployment
        );
    } else {
        if (isProjectBased && tabName == 'assignee') {
            selectedAuthoritiesorDeployment =
                formRef?.current?.getFieldValue(
                    keySelectedAuthoritiesorDeployment
                ) ||
                viewData?.form_data?.form_data[
                    keySelectedAuthoritiesorDeployment
                ];
        } else {
            selectedAuthoritiesorDeployment =
                formRef?.current?.getFieldValue(selectedRolesToBeRatedKey) ||
                viewData?.form_data?.form_data[selectedRolesToBeRatedKey];
        }
    }
    let initialValues = editMode ? viewData?.form_data : {};
    let roleList =
        !isProjectBased && tabName == 'assignee'
            ? viewData?.['role_list']
            : viewData?.[keyRoleList];
    try {
        if (selectedAuthoritiesorDeployment) {
            selectedAuthoritiesorDeployment.forEach((singlePossibleRole) => {
                let seletcedRoleTitle = roleList?.filter(
                    (singleRole) => singleRole.value == singlePossibleRole
                )?.[0]?.label;

                ratingAssigneesOrAuthorities.push({
                    key: `${collapseKey + singlePossibleRole}`,
                    render: () => {
                        return (
                            <div className="gx-mb-2 gx-ml-2">
                                <div>
                                    <Collapse>
                                        <Collapse.Panel
                                            key={singlePossibleRole}
                                            forceRender={true}
                                            header={
                                                <span>{seletcedRoleTitle}</span>
                                            }
                                        >
                                            <>
                                                <FormBuilder
                                                    meta={{
                                                        ...getMetaSelectedRatingAssigneeOrAuthority(
                                                            singlePossibleRole,
                                                            params
                                                        ),
                                                        initialValues:
                                                            initialValues,
                                                    }}
                                                    form={formRef}
                                                />
                                            </>
                                        </Collapse.Panel>
                                    </Collapse>
                                </div>
                            </div>
                        );
                    },
                });
            });
        }
    } catch (error) {
        console.log('Error while creating dynamic stage-wise fields', error);
    }
    const meta = {
        fields: [
            ...getAssigneeMetaFrAssigneeOnlyTaskBased(params).fields,
            ...ratingAssigneesOrAuthorities,
        ],
    };
    return meta;
};

export const getMetaSelectedRatingAssigneeOrAuthority = (role_id, params) => {
    const {
        tabName,
        ratingTypeKey,
        refresh,
        formRef,
        viewData,
        selectedRatingAuthority,
        staticUserKey,
        authorityIdKey,
        templateKey,
        isProjectBased,
        selectedRatingDynamicUserRole,
        HasMatchingLocationGroupKey,
        HasMatchingCustomerAccessKey,
    } = params;
    let rating_type_vs_role_id_key = `${ratingTypeKey}${role_id}`;
    let selected_rating_authority_key = `${selectedRatingAuthority}${role_id}`;
    let selected_rating_dynamic_user_role_key = `${selectedRatingDynamicUserRole}${role_id}`;
    let has_matching_location_group_fr_assignee_key = `${HasMatchingLocationGroupKey}${role_id}`;
    let has_matching_customer_access_fr_assignee_key = `${HasMatchingCustomerAccessKey}${role_id}`;
    let static_user_key = `${staticUserKey}${role_id}`;
    let template_key = `${templateKey}${role_id}`;

    const fields = [
        {
            key: rating_type_vs_role_id_key,
            widget: 'radio-group',
            onChange: refresh,
            options:
                !isProjectBased && tabName == 'assignee'
                    ? [
                          ...RATING_TYPE[tabName],
                          { value: 'dynamic_user', label: 'Dynamic User' },
                      ]
                    : RATING_TYPE[tabName],
        },
    ];
    let ratingBy = tabName == 'authority' ? 'another_authority' : 'authority';
    const ratingType =
        formRef?.current?.getFieldValue(rating_type_vs_role_id_key) ||
        viewData?.form_data?.form_data[rating_type_vs_role_id_key];
    if (ratingType == ratingBy) {
        let authoritiesFilteredOptions = [];
        if (tabName == 'authority') {
            authoritiesFilteredOptions = viewData.role_list
                ? viewData.role_list.filter(
                      (option) => option.value !== role_id
                  )
                : viewData?.authorities_list.filter(
                      (option) => option.value !== role_id
                  );
        } else {
            authoritiesFilteredOptions = viewData.role_list
                ? viewData?.role_list
                : viewData?.authorities_list;
        }
        let selectedAuthorities =
            formRef?.current?.getFieldValue(authorityIdKey);
        let getApplicableAnotherAuhtority = [];
        if (selectedAuthorities) {
            selectedAuthorities.forEach((singleAuthority) => {
                const filteredAuthority = authoritiesFilteredOptions.find(
                    (singleFilteredAuthority) =>
                        singleFilteredAuthority.value === singleAuthority
                );
                if (filteredAuthority) {
                    getApplicableAnotherAuhtority.push(filteredAuthority);
                }
            });
        }

        fields.push({
            key: selected_rating_authority_key,
            placeholder: 'Select authority',
            onChange: (value) => {
                handleClearSelect(
                    value,
                    formRef,
                    selected_rating_authority_key
                );
            },
            widget: 'select',
            options: getApplicableAnotherAuhtority || [],
            widgetProps: {
                mode: 'single',
                allowClear: true,
                showSearch: true,
                optionFilterProp: 'children',
            },
        });
    } else if (ratingType == 'static_user') {
        fields.push({
            key: static_user_key,
            widget: UserSelectorWidget,
            placeholder: 'Search User',
        });
    } else if (
        ratingType == 'dynamic_user' &&
        !isProjectBased &&
        tabName == 'assignee'
    ) {
        fields.push(
            {
                key: selected_rating_dynamic_user_role_key,
                label: 'Select role of rater',
                placeholder: 'Select role of rater',
                onChange: (value) => {
                    handleClearSelect(
                        value,
                        formRef,
                        selected_rating_dynamic_user_role_key
                    );
                },
                widget: 'select',
                options: viewData?.role_list || [],
                widgetProps: {
                    mode: 'single',
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
            },
            {
                key: has_matching_location_group_fr_assignee_key,
                label: 'Has matching location group',
                widget: 'checkbox',
            },
            {
                key: has_matching_customer_access_fr_assignee_key,
                label: 'Has matching customer access',
                widget: 'checkbox',
            }
        );
    }
    fields.push({
        key: template_key,
        label: 'Select Template',
        widget: 'select',
        options: viewData?.template_details,
        onChange: (value) => {
            handleClearSelect(value, formRef, template_key);
        },
        tooltip:
            ' Select the rating template that will be used to rate this role',
        widgetProps: {
            mode: 'single',
            allowClear: true,
        },
    });
    return {
        formItemLayout: 'vertical',
        fields,
    };
};

export const checkUserAccessInAuthorities = (
    possibleRoles,
    srvcFormData,
    userId
) => {
    let hasAccess = false;
    let srvcReqAuthoritiesId = [];
    possibleRoles.forEach((singleAuthority) => {
        let authority_key = 'authority_' + singleAuthority;
        let authority_id = srvcFormData[authority_key];
        if (authority_id && authority_id != '') {
            srvcReqAuthoritiesId.push(authority_id);
        }
    });
    //check login user is exists in srvcReqAuthoritiesId ?
    // if exists then return true
    if (srvcReqAuthoritiesId.length > 0 && userId) {
        hasAccess = srvcReqAuthoritiesId.includes(userId);
    }
    return hasAccess;
};

export const getSrvcCategoryFieldOptions = (formData, isServiceProvider) => {
    let categoryFieldKey;
    if (isServiceProvider) {
        categoryFieldKey = 'srvc_prvdr_category_field';
    } else {
        categoryFieldKey = 'srvc_category_field';
    }
    const originalFields = decodeFieldsMetaFrmJson(
        isServiceProvider
            ? formData.sp_cust_fields_json
            : formData.srvc_cust_fields_json
    );
    const categoryFieldValue = formData[categoryFieldKey];
    const srvcCategoryFieldOptions = originalFields.filter((originalField) => {
        return originalField.key == categoryFieldValue;
    });
    return srvcCategoryFieldOptions[0]?.options;
};
export function removeCommonElements(a1, a2) {
    let data = a2?.filter((item) => !a1?.includes(item));
    return data;
}

export const getSbtskTimeFieldsMetaFrReqCreation = (
    startOfDay,
    endOfDay,
    natureOfRequest,
    formRef,
    viewData,
    refreshForm
) => {
    const startTimeFrEndTime = formRef.current?.getFieldValue('start_time')
        ? formRef.current?.getFieldValue('start_time')
        : viewData?.form_data?.form_data?.start_time;

    let srvcTimeFields = [];

    if (natureOfRequest != 'project_based') {
        srvcTimeFields = [
            {
                key: 'start_time',
                label: 'Start Time',
                widget: TimePickerWidget,
                widgetProps: {
                    beginLimit: startOfDay,
                    endLimit: endOfDay,
                    step: 15,
                    onChange: (value) => {
                        refreshForm();
                    },
                },
                colSpan: 2,
            },
            {
                key: 'end_time',
                label: 'End Time',
                widget: TimePickerWidget,
                widgetProps: {
                    beginLimit: startTimeFrEndTime
                        ? startTimeFrEndTime
                        : startOfDay,
                    endLimit: endOfDay,
                    step: 15,
                },
                colSpan: 2,
            },
        ];
    }

    return srvcTimeFields;
};

export const renderCommentWithLinks = (value, shouldRenderLinks = false) => {
    if (!shouldRenderLinks) {
        return value;
    }

    // Regular expression to match URLs
    const urlRegex = /(https?:\/\/[^\s]+)/g;

    // Split the comment text into parts containing URLs and non-URLs
    const parts = value.split(urlRegex);

    // Map over the parts and convert URLs to anchor tags
    const renderedParts = parts.map((part, index) => {
        if (part.match(urlRegex)) {
            // If part is a URL, return it wrapped in an anchor tag
            return (
                <a
                    key={index}
                    href={part}
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    {part}
                </a>
            );
        } else {
            // If part is not a URL, return it as is
            return part;
        }
    });

    // Return the rendered parts joined together
    return renderedParts;
};

export const formatTimeRange = (startTime, endTime) => {
    if (!startTime && !endTime) {
        return null;
    }
    // Check both times, and replace missing values with 'Not Specified'
    const formattedStartTime = startTime ? startTime : 'Not Specified';
    const formattedEndTime = endTime ? endTime : 'Not Specified';

    return `${formattedStartTime} - ${formattedEndTime}`;
};

export const getAvgGaiRatingOfSubTasks = (data) => {
    let geminiRatings = { sum: 0, count: 0, avg: null };
    data.forEach((_eachSubTask) => {
        let _eachSubTaskRatingJson = _eachSubTask?.gai_rating;
        if (!_eachSubTaskRatingJson) return null;
        if (
            _eachSubTaskRatingJson?.gemini?.rating &&
            Number(_eachSubTaskRatingJson?.gemini?.rating)
        ) {
            geminiRatings.count = geminiRatings.count + 1;
            geminiRatings.sum =
                geminiRatings.sum +
                Number(_eachSubTaskRatingJson?.gemini?.rating);
        }
    });
    if (geminiRatings.count == 0) return null;
    if (geminiRatings.count !== 0) {
        geminiRatings.avg = (geminiRatings.sum / geminiRatings.count).toFixed(
            2
        );
        return geminiRatings.avg;
    }
};

export const getServiceRequestDateMeta = (formref) => {
    const meta = {
        fields: [
            {
                key: 'request_req_date',
                label: 'Req. Service Date',
                colSpan: 2,
                widgetProps: {
                    disabledDate: (current) =>
                        current && current > moment().add(2, 'month'),
                    style: { width: '100%' },
                    onChange: (value, dateString) => {
                        formref.current.setFieldsValue({
                            request_req_date: moment.utc(dateString),
                        });
                    },
                },
                widget: 'date-picker',
            }           
        ],
    };
    return meta;
};
