import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { message, Modal } from 'antd';
import CSVExporter from '../../components/wify-utils/CSVExporter';
import {
    getAddressInfoMeta,
    getCustomerInfoMeta,
    getCustomFieldsFrmConfig,
    getRequestInfoMeta,
    getFeedbackFieldsFrmConfig,
    getOnFieldTaskInfoMeta,
    getTransitionOverviewMeta,
    getSpCustomFieldsFrmConfig,
    getVerticalsFrmConfig,
    getOrganisationinfoMeta,
    getPiInfo,
    getLineItem,
    getCalculatedTask,
    getSpCustomFileMetaFrmConfig,
    getSPAuthorityFrmConfig,
    getChangeLockMeta,
    getBrandAuthorityFrmConfig,
    getPayoutInfo,
    getPayoutInfoFrmConfig,
} from './helpers';
import ConfigHelpers from '../../util/ConfigHelpers';
import { getCurrentDateAndTimeFrDisplay } from '../../util/helpers';

// const protoUrl = "/services/proto";
const submitUrl = '/services/export';

class ExporterModal extends Component {
    constructor(props) {
        super(props);
    }
    initState = {
        render_helper: false,
        visible: false,
        isFormSubmitting: false,
        viewData: undefined,
        isLoadingViewData: false,
        editMode: this.props.editMode,
        error: '',
        srvcDetails: this.props.srvcDetails,
        srvc_id: this.props.srvcDetails?.srvc_id,
        fileSections: [],
        filesBySection: {},
        sectionWiseUploaderReady: {},
        isLoadingCustomerDetails: false,
        autoFillCustId: '',
        custMobNoFrHis: '',
    };
    state = this.initState;

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.showEditor != this.props.showEditor) {
            this.setState({
                render_helper: !this.state.render_helper,
                visible: this.props.showEditor,
            });
        }
    }

    handleCancel = () => {
        this.setState({
            visible: false,
        });
        this.updateClosureToParent();
    };

    updateClosureToParent() {
        if (this.props.onClose != undefined) {
            this.props.onClose();
        }
        this.setState({
            refreshOnUpdate: true,
            ...this.initState,
        });
    }

    isCustomerRequests() {
        return this.props.isCustomerRequests;
    }
    getMetaFrExporter() {
        console.log(
            'ExporterModal :: Consumer_feedback :: ConfigHelpers.isServiceProvider() && this.props.isCustomerRequests :: ',
            ConfigHelpers.isServiceProvider() && this.props.isCustomerRequests
        );
        
        let temp_specific_section_meta = [];
        const hidden_fields = ConfigHelpers.isServiceProvider()
            ? this.props?.sp_hidden_fields
            : this.props?.hidden_fields;
        const filteredFields = getCustomFieldsFrmConfig(
            this.props?.srvcConfigData
        )?.fields.filter(
            (field) =>
                !this.props?.hidden_fields?.includes(field?.key) &&
                field?.cust_component !== 'legend' &&
                field?.cust_component !== 'linebreak'
        );
        if (filteredFields?.length > 0) {
            const specificDetailsHeader =
                this.props.srvcConfigData?.rename_specific_details_label ||
                'Specific Details';
            temp_specific_section_meta.push({
                key: 'specific_fields', //this should be used for select all checkbox
                text: specificDetailsHeader,
                fields_meta: [...filteredFields],
            });
        }
        let temp_vertical_section_meta = [];
        let calculated_task_data = [];
        let line_item_data = [];
        let payout_info = [];
        let pi_info = [];
        let sp_attachment_field_info = [];
        let authority_section_meta = [];
        const filteredVerticalFields = getVerticalsFrmConfig(
            this.props.srvcConfigData.vertical_form_data
        ).fields.filter(
            (field) =>
                !hidden_fields?.includes(field?.key) &&
                field?.cust_component !== 'legend' &&
                field?.cust_component !== 'linebreak'
        );
        if (filteredVerticalFields?.length > 0) {
            temp_vertical_section_meta.push({
                key: 'SP_specific_fields', //this should be used for select all checkbox
                text: 'SP specific fields',
                fields_meta: [...filteredVerticalFields],
            });
        }
        if (this.props.filters?.verticals_list?.length > 0) {
            calculated_task_data.push({
                key: 'calculated_task_data',
                text: 'Calculated task data',
                fields_meta: [...getCalculatedTask().fields],
            });
            if (
                ConfigHelpers.canUserSeeBillingTab(
                    this.props.srvcConfigData?.vertical_form_data
                )
            ) {
                line_item_data.push({
                    key: 'line_item_data',
                    text: 'Line Item Data',
                    fields_meta: [...getLineItem().fields],
                });

                pi_info.push({
                    key: 'pi_info',
                    text: 'PI info',
                    fields_meta: [...getPiInfo().fields],
                });
            }
            let customFileMeta = getSpCustomFileMetaFrmConfig(
                this.props.srvcConfigData.vertical_form_data
            ).fields;
            if (customFileMeta?.length > 0) {
                sp_attachment_field_info.push({
                    key: 'sp_attachment_field_info',
                    text: 'Attachment Field info',
                    fields_meta: [...customFileMeta],
                });
            }
            if (
                this.props?.sp_authority_data &&
                this.props.srvcConfigData?.vertical_form_data?.authority_id
                    ?.length > 0
            ) {
                authority_section_meta.push({
                    key: 'sp_authorities',
                    text: 'SP authorities',
                    fields_meta: [
                        ...getSPAuthorityFrmConfig(
                            this.props.sp_authority_data,
                            this.props.srvcConfigData.vertical_form_data
                        ).fields,
                    ],
                });
            }

            if (
                this.props.srvcConfigData?.vertical_form_data
                    ?.enable_sp_payouts &&
                ConfigHelpers.doesUserHaveOneOfTheRoleToDownloadPayoutInfo(
                    this.props.srvcConfigData?.vertical_form_data
                        ?.sp_who_will_not_be_able_to_see_the_sp_payout_tab
                )
            ) {
                payout_info.push({
                    key: 'payout_info',
                    text: 'Payout Info',
                    fields_meta: [
                        ...getPayoutInfo(
                            this.props.srvcConfigData.vertical_form_data
                                ?.srvc_type_sp_payouts_config
                        ).fields,
                    ],
                });
            }
        }
        let brand_authority = [];
        if (
            !ConfigHelpers.isServiceProvider() &&
            this.props.srvcConfigData.srvc_authorities?.length > 0
        ) {
            brand_authority.push({
                key: 'brand_authorities',
                text: 'Brand Authorities',
                fields_meta: [
                    ...getBrandAuthorityFrmConfig(
                        this.props.brand_authority_data,
                        this.props.srvcConfigData.srvc_authorities
                    ).fields,
                ],
            });
        }
        let temp_feedback_section_meta = [];
        if (
            getFeedbackFieldsFrmConfig(this.props.srvcConfigData).fields
                ?.length > 0
        ) {
            temp_feedback_section_meta.push({
                key: 'feedback_fields',
                text: 'Feedback details',
                fields_meta: [
                    ...getFeedbackFieldsFrmConfig(this.props.srvcConfigData)
                        .fields,
                ],
            });
        }
        let temp_transition_overview_meta = [];
        if (
            getTransitionOverviewMeta(this.props.possibleStatus).fields
                ?.length > 0
        ) {
            temp_transition_overview_meta.push({
                key: 'transition_overview_fields',
                text: 'Transition Overview',
                fields_meta: [
                    ...getTransitionOverviewMeta(this.props.possibleStatus)
                        .fields,
                ],
            });
        }
        let sp_specific_section_meta = [];
        if (ConfigHelpers.isServiceProvider()) {
            if (
                getSpCustomFieldsFrmConfig(this.props.sp_config_data).fields
                    ?.length > 0
            ) {
                sp_specific_section_meta.push({
                    key: 'sp_specific_fields',
                    text: 'Service provider specific details',
                    fields_meta: [
                        ...getSpCustomFieldsFrmConfig(this.props.sp_config_data)
                            .fields,
                    ],
                });
            }
        }
        let sp_org_info_meta = [];
        if (ConfigHelpers.isServiceProvider() && !this.props.isCustAcces) {
            sp_org_info_meta.push({
                key: 'org_info',
                text: 'Organisation Info',
                fields_meta: [...getOrganisationinfoMeta().fields],
            });
        }
        let AssigneePhnNoVisible = [];
        if (ConfigHelpers.isServiceProvider() && !this.props.isCustAcces) {
            AssigneePhnNoVisible.push({
                key: 'assignee_phone_no.',
                label: 'Assignee Phone No.',
            });
        }
        let TaskDate = [];
        if (ConfigHelpers.isServiceProvider() && !this.props.isCustAcces) {
            TaskDate.push(
                {
                    key: 'sp_first_task_date',
                    label: 'SP First task date',
                },
                {
                    key: 'sp_last_task_date',
                    label: 'SP Last task date',
                }
            );
        }
        let gaiRemarks = [];
        if (ConfigHelpers.isServiceProvider() && !this.props.isCustAcces) {
            gaiRemarks.push({
                key: 'gai_remarks',
                label: 'GAI remarks',
            });
        }

        console.log(
            'ExporterModal :: Consumer_feedback :: ConfigHelpers.isServiceProvider() && this.props.isCustomerRequests :: ',
            ConfigHelpers.isServiceProvider() && this.props.isCustomerRequests
        );

        return {
            email_id: `${ConfigHelpers.getUserEmailId()}`,
            subject: `WIFY TMS ${this.props.srvcDetails?.srvc_title || 'Customer requests'} dump ${getCurrentDateAndTimeFrDisplay()}`,
            filters: this.props.filters || {},
            section_wise_meta: [
                ...sp_org_info_meta,
                {
                    key: 'customer_info_fields', //this should be used for select all checkbox
                    text: 'Customer Info',
                    fields_meta: [...getCustomerInfoMeta().fields],
                },
                {
                    key: 'address_info_fields', //this should be used for select all checkbox
                    text: 'Address Info',
                    fields_meta: [
                        ...getAddressInfoMeta().fields,
                        {
                            key: 'location_group',
                            label: 'Location Group',
                        },
                    ],
                },
                ...temp_specific_section_meta,
                {
                    key: 'request_info_fields', //this should be used for select all checkbox
                    text: 'Request Info',
                    fields_meta: [
                        ...getRequestInfoMeta(this.props.srvcDetails).fields,
                        ...(!ConfigHelpers.isServiceProvider() &&
                        this.props?.srvcConfigData?.srvc_enable_srvc_prvdr
                            ? [
                                  {
                                      key: 'srvc_prvdr',
                                      label: 'Service provider',
                                      colSpan: 2,
                                  },
                              ]
                            : []),
                    ],
                },

                ...temp_feedback_section_meta,
                ...((ConfigHelpers.isServiceProvider() && this.props.isCustomerRequests) ? [    {
                        key: "pnl_info",
                        text: "P&L Info",
                        fields_meta: [
                            { key: "gm_per", label: "GM%" },
                            { key: "net_gm", label: "Net GM" },
                            { key: "total_revenue", label: "Total Revenue" },
                            { key: "total_cost", label: "Total Cost" }
                        ]
                }] : []),
                {
                    key: 'onfield_task_fields', //this should be used for select all checkbox
                    text: 'Onfield task info',
                    fields_meta: [
                        ...getOnFieldTaskInfoMeta().fields,
                        ...AssigneePhnNoVisible,
                        ...TaskDate,
                        ...gaiRemarks,
                    ],
                },
                ...((ConfigHelpers.isServiceProvider() && !this.props.isCustAcces)
                    ? [
                          {
                              key: 'Consumer_feedback',
                              text: 'Consumer Feedback',
                              fields_meta: [
                                  { key: 'cust_rating', label: 'Rating' },
                              ],
                          },
                      ]
                    : []),

                ...brand_authority,
                ...temp_transition_overview_meta,
                ...sp_specific_section_meta,
                ...temp_vertical_section_meta,
                ...authority_section_meta,
                //This is temporary beecause currently GAI rating is possiible in ServiceProvider only
                ...(ConfigHelpers.isServiceProvider() &&
                this.isCustomerRequests()
                    ? [
                          {
                              key: 'gai_info_fields', //this should be used for select all checkbox
                              text: 'GAI Info',
                              fields_meta: [...this.getGAIInfoMeta().fields],
                          },
                      ]
                    : []),
                {
                    key: 'locked_for_change',
                    text: 'Locked for change',
                    fields_meta: [
                        ...getChangeLockMeta(this.props.isCustAcces).fields,
                    ],
                },
                ...calculated_task_data,
                ...line_item_data,
                ...payout_info,
                ...pi_info,
                ...sp_attachment_field_info,
            ],
        };
    }

    getGAIInfoMeta = () => {
        const meta = {
            columns: 4,
            formItemLayout: null,
            fields: [
                {
                    key: 'avg_gai_rating',
                    label: 'Avg. GAI rating',
                },
                {
                    key: 'no_of_gai_rated_tasks',
                    label: 'No. of GAI rated tasks',
                },
            ],
        };
        return meta;
    };
    render() {
        const {
            isFormSubmitting,
            visible,
            isLoadingViewData,
            error,
            viewData,
            currentStep,
            srvcDetails,
            fileSections,
        } = this.state;
        let url =
            submitUrl +
            '/' +
            (this.props.srvcDetails?.srvc_id || 0) +
            '/' +
            (this.props.isCustAcces ? '1' : '0');

        return visible ? (
            <Modal
                // title={
                //     <span><i className={`icon ${srvcDetails?.srvc_icon} gx-mr-2 h1`}></i> {editorTitle}</span>
                // }
                visible={visible}
                onOk={this.handleOk}
                confirmLoading={isFormSubmitting}
                width={700}
                // style={{
                //     marginTop:'-70px'
                // }}
                // bodyStyle={{
                //     minHeight:'85vh',
                //     padding:'18px',
                //     paddingTop: '0px'
                // }}
                footer={null}
                onCancel={this.handleCancel}
                data-testid="exporter-modal"
            >
                <CSVExporter
                    // demoMode
                    exportMeta={this.getMetaFrExporter()}
                    submitUrl={url}
                    onSubmitComplete={(resp) => {
                        message.success(
                            'Email will be sent shortly, Export data request added successfully'
                        );
                        this.handleCancel();
                    }}
                />
            </Modal>
        ) : (
            <></>
        );
    }
}

ExporterModal.propTypes = {};

export default ExporterModal;
