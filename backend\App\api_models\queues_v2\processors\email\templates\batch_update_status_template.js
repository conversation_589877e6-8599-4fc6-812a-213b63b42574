class batch_update_status_template {
    getBatchUpdateStatusEmailTemplate = (notifyData) => {
        const {
            batchNumber,
            totalBatches,
            batchSize,
            failureResults,
            successResults,
        } = notifyData;

        let html = `
        <html lang="en">
        <body>
            <div>
                <p>Hello,</p>
                <p>Below are the batch update results:</p>
                <table style="width:60%; border:1px solid #ccc; border-collapse: collapse; font-family: Arial, sans-serif; font-size: 14px;">
                    <tr style="background-color: #f2f2f2;">
                        <td style="border:1px solid #ccc; padding: 8px;">Batch Number</td>
                        <td style="border:1px solid #ccc; padding: 8px;">${batchNumber} of ${totalBatches}</td>
                    </tr>
                    <tr>
                        <td style="border:1px solid #ccc; padding: 8px;">Total Records</td>
                        <td style="border:1px solid #ccc; padding: 8px;">${batchSize}</td>
                    </tr>
                    <tr>
                        <td style="border:1px solid #ccc; padding: 8px;">Failed Records</td>
                        <td style="border:1px solid #ccc; padding: 8px;">${failureResults.length}</td>
                    </tr>
                    <tr>
                        <td style="border:1px solid #ccc; padding: 8px;">Success Records</td>
                        <td style="border:1px solid #ccc; padding: 8px;">${successResults.length}</td>
                    </tr>
                </table>
                <br />
        `;

        if (failureResults.length > 0) {
            html += `
                <h4>❌ Failed TMS IDs:</h4>
                <ul>`;
            failureResults.forEach((idObj) => {
                html += `<li>${JSON.stringify(idObj)}</li>`;
            });
            html += `</ul>`;
        }

        if (successResults.length > 0) {
            html += `
                <h4>✅ Success TMS IDs:</h4>
                <ul>`;
            successResults.forEach((idObj) => {
                html += `<li>${JSON.stringify(idObj)}</li>`;
            });
            html += `</ul>`;
        }

        return html;
    };

    getAllBatchProcessCompletedEmailTemplate = (notifyData) => {
        return `<html>
            <body style="font-family: Arial, sans-serif; font-size: 14px; color: #333;">
                <div style="max-width: 600px; margin: auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px;">
                    <h2 style="color: #2e7d32;">✔ Batch Update Successful</h2>
                    <p>Dear User,</p>
                    <p>
                        The batch process for updating the status of all service requests has been
                        <strong>successfully completed</strong>.
                    </p>
                    <p>You may now review the updated requests.</p>

                    <h3 style="margin-top: 30px; color: #444;">📊 Summary</h3>
                    <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Total Batches</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${notifyData?.totalBatches}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;"><strong>Total Records Processed</strong></td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${notifyData?.totalRecords}</td>
                        </tr>
                    </table>

                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #ddd;" />
                    <div class="footer" style="font-size: 12px; color: #777;">
                        This is an automated message. Please do not reply directly to this email.
                    </div>
                </div>
            </body>
        </html>`;
    };
}

module.exports = new batch_update_status_template();
