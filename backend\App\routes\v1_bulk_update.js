let express = require('express');
let router = express.Router();
const { getUserContextFrmReq } = require('../api_models/utils/authrizor');

/**
 * Bulk update service request statuses
 * Jobs are created in batches of 100 requests
 */
router.post('/service-request-status', async function (req, res, next) {
    const model = setParamsToModel(req);
    try {
        const { batch_data } = req.body;
        if (
            !batch_data ||
            !Array.isArray(batch_data) ||
            batch_data.length === 0
        ) {
            return res
                .status(400)
                .send('Invalid batch_data. Expected non-empty array.');
        }

        model
            .updateServiceRequestStatusBatch(batch_data)
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    } catch (error) {
        console.error('service-request-status :: error :: ', error);
        res.status(500).send('Internal server error');
    }
});

const setParamsToModel = (req) => {
    const model = require('../api_models/bulk_update_model').getInstance();
    model.database = req.app.get('db');
    model.ip_addr = req.ip;
    model.user_agent = req.get('User-Agent');
    model.user_context = getUserContextFrmReq(req);
    return model.getFreshInstance(model);
};

module.exports = router;
