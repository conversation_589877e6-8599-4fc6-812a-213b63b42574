{"name": "wify-tms", "version": "1.1.0", "private": true, "dependencies": {"@amcharts/amcharts3-react": "^3.1.0", "@ant-design/compatible": "^1.0.2", "@ant-design/icons": "^4.1.0", "@googlemaps/js-api-loader": "^1.13.1", "@react-google-maps/api": "1.11.0", "@zxing/library": "^0.20.0", "amcharts3": "^3.21.15", "antd": "4.18.8", "antd-form-builder": "2.1.2", "antd-theme-webpack-plugin": "^1.3.4", "app-redux": "^2.0.3", "axios": "0.21.2", "cli-confirm": "^1.0.1", "connected-react-router": "^6.6.1", "cross-env": "^7.0.3", "dotenv": "^10.0.0", "draft-js": "^0.10.5", "draftjs-to-html": "^0.8.4", "env-cmd": "^10.1.0", "firebase": "^7.6.1", "html-to-draftjs": "^1.4.0", "immutability-helper": "^3.1.1", "instantsearch.css": "^7.3.1", "js-yaml": "^4.1.0", "less": "^3.10.3", "less-loader": "^5.0.0", "less-vars-to-js": "^1.3.0", "lodash": "^4.17.21", "moment": "^2.24.0", "mutation-observer": "^1.0.3", "nprogress": "^0.2.0", "prop-types": "^15.7.2", "rc-pagination": "^2.2.2", "react": "^16.12.0", "react-beautiful-dnd": "^13.1.0", "react-big-calendar": "^0.23.0", "react-bootstrap-sweetalert": "^5.1.9", "react-ckeditor-component": "^1.1.0", "react-color": "^2.17.3", "react-copy-to-clipboard": "^5.0.4", "react-countup": "6.3.2", "react-custom-scrollbars": "^4.2.1", "react-d3-tree": "1.16.1", "react-dnd": "^10.0.2", "react-dnd-html5-backend": "^10.0.2", "react-dom": "^16.12.0", "react-draft-wysiwyg": "^1.14.5", "react-dropzone-uploader": "^2.11.0", "react-excel-renderer": "^1.1.0", "react-export-excel": "^0.5.3", "react-file-icon": "^1.0.0", "react-file-previewer": "^0.6.3", "react-file-viewer": "^1.2.1", "react-flip-move": "^3.0.4", "react-hot-loader": "^4.12.18", "react-html5-camera-photo": "^1.5.11", "react-icons": "5.1.0", "react-instantsearch": "^6.1.0", "react-instantsearch-dom": "^6.1.0", "react-intl": "^3.9.3", "react-notifications": "^1.4.3", "react-placeholder": "^3.0.2", "react-qr-reader": "^3.0.0-beta-1", "react-qr-scanner": "^1.0.0-alpha.8", "react-rangeslider": "^2.2.0", "react-redux": "^7.1.3", "react-router-dom": "^5.1.2", "react-router-redux": "^4.0.8", "react-scripts": "^3.4.0", "react-select": "^4.3.0", "react-simple-maps": "^0.12.1", "react-slick": "^0.25.2", "react-star-rating-component": "^1.4.1", "react-star-ratings": "^2.3.0", "react-stickies": "^0.0.16", "react-swipeable-list": "^1.9.1", "react-type-animation": "^3.2.0", "react-webcam": "^7.0.1", "recharts": "^2.0.0-beta.1", "redux": "^4.0.5", "redux-devtools-extension": "^2.13.9", "redux-form": "^8.3.7", "redux-logger": "^3.0.6", "redux-saga": "^1.1.3", "redux-thunk": "^2.3.0", "rheostat": "^4.1.0", "slick-carousel": "^1.8.1", "uglifyjs-webpack-plugin": "^2.2.0", "url-search-params": "^1.1.0", "uuid": "^3.3.2", "victory": "^34.0.0", "xlsx": "^0.18.5"}, "scripts": {"start": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-app-rewired start", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider react-app-rewired build", "create-coverage": "react-scripts test --coverage -- --config=jest.config.js", "test": "react-app-rewired test --watchAll -- --config=jest.config.js", "test-units": "react-app-rewired test -- --config=jest.config.js", "less": "lessc --js src/styles/wieldy.less public/css/style.css", "start-as-prod": "env-cmd -f .env.production cross-env env_config=production react-app-rewired start", "build-uat": "env-cmd -f .env.test cross-env env_config=test react-app-rewired build", "build-dev": "env-cmd -f .env cross-env env_config=dev react-app-rewired build", "deploy-to-uat": "yarn build-uat && aws s3 sync build/ s3://wify-tms-demo.wify.co.in --acl public-read", "build-prod": "cross-env env_config=production react-app-rewired build", "deploy-to-prod": "cli-confirm \"Confirm push build to production (y/n)? \" && yarn build-prod && aws s3 sync build/ s3://tms.wify.co.in --acl public-read && yarn refresh-prod", "refresh-prod": "aws cloudfront create-invalidation --distribution-id E1N5AJGRIBH2IQ --paths \"/*\" ", "version-check": "node check-version.js", "prettier-format": "prettier --write ."}, "eslintConfig": {"extends": "react-app", "rules": {"no-unused-vars": "off"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/preset-env": "^7.24.8", "@babel/preset-react": "^7.24.7", "@swc/core": "^1.7.14", "@swc/jest": "^0.2.36", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^12.1.1", "@testing-library/user-event": "^13.2.1", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "babel-plugin-import": "^1.13.0", "customize-cra": "^0.9.1", "jest": "24.9.0", "prettier": "^3.3.3", "react-app-rewired": "^2.1.5", "regenerator-runtime": "^0.14.1", "typescript": "^5.8.2"}, "resolutions": {"glob": "7.2.3", "commander": "2.20.3", "nwsapi": "2.2.13", "react-countup": "6.3.2", "pretty-format": "26.6.2", "expect": "24.9.0"}}