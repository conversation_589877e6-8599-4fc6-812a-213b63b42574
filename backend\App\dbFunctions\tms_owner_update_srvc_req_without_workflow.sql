-- DROP FUNCTION public.tms_owner_update_srvc_req_without_workflow(json);

CREATE OR REPLACE FUNCTION public.tms_owner_update_srvc_req_without_workflow(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Declarations
    status boolean;
    message text;
    resp_data json;
    batch_data_ json;
    ip_address_ text;
    user_agent_ text;
    
    -- Temp variables
    record_data json;
    tms_id_ text;
    new_status_title text;
    comment_ text;
    service_type_id_ integer;
    provider_id_ integer;
    entry_id_ integer;
    org_id_ integer;
    current_status_key_ text;
    status_key_ text;
	current_req_status_ text;
    srvc_type_id_ integer;
	is_same_status boolean := false;
    
    -- System user
    sys_user_res json;
    sys_user_id uuid;
    
    -- Form data for update
    update_req_form_data_ json;
    timeline_comment_form_data json;
    
    -- Results tracking
    success_results json[] := '{}';
    failure_results json[] := '{}';
    success_count integer := 0;
    failure_count integer := 0;
    srvc_req_ids text[] := '{}';
    failed_req_ids text[] := '{}';
    
    -- Loop counter
    i integer;
BEGIN
    status := false;
    message := 'Internal_error';
    
    -- Extract batch data and other parameters from form_data
    batch_data_ := form_data->'batch_data';
    ip_address_ := form_data->>'ip_address';
    user_agent_ := form_data->>'user_agent';
    
    -- Loop through each record in the batch
    FOR i IN 0..json_array_length(batch_data_) - 1 LOOP
        BEGIN
            -- Extract record data
            record_data := batch_data_->i;
            tms_id_ := record_data->>'tms_id';
            new_status_title := trim(record_data->>'new_status');
            comment_ := record_data->>'comment';
            service_type_id_ := (record_data->>'service_type_id')::integer;
            provider_id_ := (record_data->>'provider_id')::integer;

            -- Check if both provider_id and service_type_id are NULL
            IF provider_id_ IS NULL AND service_type_id_ IS NULL THEN
                failure_results := array_append(
                    failure_results,
                    json_build_object(
                        'tms_id', tms_id_,
                        'status', false,
                        'message', 'At least one of provider_id or service_type_id must be provided'
                    )
                );
                failed_req_ids := array_append(failed_req_ids, tms_id_ ::text);
                failure_count := failure_count + 1;
                CONTINUE;
            END IF;

            -- Validate required fields
            IF tms_id_ IS NULL OR new_status_title IS NULL THEN
                failure_results := array_append(
                    failure_results,
                    json_build_object(
                        'tms_id', tms_id_,
                        'status', false,
                        'message', 'Missing required fields: tms_id and new_status'
                    )
                );
                failed_req_ids := array_append(failed_req_ids, tms_id_ ::text);
                failure_count := failure_count + 1;
                CONTINUE;
            END IF;            
            
            -- Get the srvc req db_id for tms_id by either prvdr or srvc_type id
            SELECT req.db_id, req.org_id, req."status", req.srvc_type_id 
              INTO entry_id_, org_id_, current_status_key_, srvc_type_id_
              FROM cl_tx_srvc_req req
             WHERE req.display_code = tms_id_
               AND (
                  (provider_id_ IS NULL OR req.srvc_prvdr = provider_id_)
               AND
                  (service_type_id_ IS NULL OR req.srvc_type_id = service_type_id_)
                )
               AND req.is_deleted IS NOT TRUE
             LIMIT 1;
            
            IF entry_id_ IS NULL THEN
                failure_results := array_append(
                    failure_results,
                    json_build_object(
                        'tms_id', tms_id_,
                        'status', false,
                        'message', 'Service request not found'
                    )
                );
                failed_req_ids := array_append(failed_req_ids, tms_id_ ::text);
                failure_count := failure_count + 1;
                CONTINUE;
            END IF;
            
            -- Get status key for the new status title 
            IF srvc_type_id_ IS NOT NULL THEN
				-- Get title for current status
				SELECT ss.title
				  INTO current_req_status_
				  FROM cl_cf_srvc_statuses ss
				 WHERE ss.srvc_id = srvc_type_id_
				   AND ss.status_key = current_status_key_
				 LIMIT 1;

                -- Get status key for new status title
                SELECT ss.status_key INTO status_key_
                  FROM cl_cf_srvc_statuses ss
                 WHERE ss.srvc_id = srvc_type_id_ 
                   AND ss.title = new_status_title
                 LIMIT 1;

				-- If no matching status found add failure record
                IF status_key_ IS NULL THEN
                    failure_results := array_append(
                    failure_results,
                    json_build_object(
                        'tms_id', tms_id_,
                        'status', false,
                        'message', 'Invalid request status : ' || new_status_title
                        )
                    );
                    failed_req_ids := array_append(failed_req_ids, tms_id_ ::text);
                    failure_count := failure_count + 1;
                    CONTINUE;
                END IF;

            END IF;

            -- Get or create system user for the organization
            -- Use provider_id_ as org_id if it is present, otherwise use org_id_ from the service request
            sys_user_res := tms_get_or_create_system_usr(
                json_build_object(
                    'org_id', COALESCE(provider_id_, org_id_),
                    'ip_address', COALESCE(ip_address_, 'system_batch_update'),
                    'user_agent', COALESCE(user_agent_, 'system_batch_update')
                )
            );
            sys_user_id := sys_user_res->'data'->>'usr_id';
            
            -- Prepare form_data for tms_create_service_request
            update_req_form_data_ := json_build_object(
                'new_status', status_key_,
                'host_d', 'system_batch_update',
                'is_frm_frontend', false,
                'org_id', org_id_,
                'usr_id', sys_user_id,
                'ip_address', COALESCE(ip_address_, 'system_batch_update'),
                'user_agent', COALESCE(user_agent_, 'system_batch_update'),
                'srvc_type_id', srvc_type_id_,
                'is_customer_access', 0
            );
            
           -- dont update status if already same
            IF current_status_key_ = status_key_ THEN
				is_same_status = true;
            ELSE 
               	PERFORM tms_create_service_request(update_req_form_data_, entry_id_);
            END IF;

			IF comment_ IS NULL THEN
			    -- Record failure for null comment
			    failure_results := array_append(
			        failure_results,
			        json_build_object(
			            'tms_id', tms_id_,
			            'status', false,
			            'message', 'Comment is required but not provided'
			        )
			    );			
			    failed_req_ids := array_append(failed_req_ids, tms_id_ ::text);
			    failure_count := failure_count + 1;
			ELSE
                -- update comment on request
				timeline_comment_form_data := json_build_object(
			            'comment', comment_,
			            'update_for_comment', true,
						'host_d', 'system_batch_update',
	                    'is_frm_frontend', false,
	                    'org_id', org_id_,
	                    'usr_id', sys_user_id,
	                    'ip_address', COALESCE(ip_address_, 'system_batch_update'),
	                    'user_agent', COALESCE(user_agent_, 'system_batch_update'),
	                    'srvc_type_id', srvc_type_id_,
	                    'is_customer_access', 0
			        );
			    PERFORM tms_create_service_request(timeline_comment_form_data, entry_id_);
			END IF;
			
            -- Add to successful service request IDs array
            srvc_req_ids := array_append(srvc_req_ids, entry_id_ ::text);
            -- Record success
            success_results := array_append(
                success_results,
                json_build_object(
                    'tms_id', tms_id_,
                    'entry_id', entry_id_,
                    'org_id', org_id_,
                    'status', true,
                    'message', 
			            CASE 
			                WHEN is_same_status THEN 
			                    'Status is already set to ' || current_req_status_ || ' (' || current_status_key_ || ')'
			                ELSE 
			                    'Status updated from ' || current_req_status_ || ' (' || current_status_key_ || ') to ' || new_status_title || ' (' || status_key_ || ')'
			            END
                )
            );
            success_count := success_count + 1;
            
        EXCEPTION WHEN OTHERS THEN
            -- Record failure with error message
            failure_results := array_append(
                failure_results,
                json_build_object(
                    'tms_id', tms_id_,
                    'status', false,
                    'message', 'Error: ' || SQLERRM
                )
            );
            
            -- Add the TMS ID to failed_req_ids
            failed_req_ids := array_append(failed_req_ids, tms_id_ ::text);
            failure_count := failure_count + 1;
        END;
    END LOOP;
    
    -- Build response
    status := true;
    message := 'success';
    resp_data := json_build_object(
        'totalRecords', json_array_length(batch_data_),
        'successCount', success_count,
        'failureCount', failure_count,
        'success_req_ids', srvc_req_ids,
        'failed_req_ids', failed_req_ids,
        'success_results', array_to_json(success_results),
        'failure_results', array_to_json(failure_results)
    );
    
    RETURN json_build_object('status', status, 'message', message, 'data', resp_data);
END;
$function$
;
