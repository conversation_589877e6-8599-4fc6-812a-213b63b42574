import React from 'react';

const basicMarkdownToHTML = (markdown = '') => {
    return (
        markdown
            // Escape HTML
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')

            // Headers
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')

            // Bold and Italic
            .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/gim, '<em>$1</em>')
            .replace(/_(.*?)_/gim, '<em>$1</em>')

            // Inline code
            .replace(/`([^`]+)`/gim, '<code>$1</code>')

            // Links
            .replace(
                /\[([^\]]+)\]\(([^)]+)\)/gim,
                '<a href="$2" target="_blank">$1</a>'
            )

            // Line breaks
            .replace(/\n$/gim, '<br />')
            .replace(/\n/gim, '<br />')
    );
};

const MarkdownRenderer = ({ markdownText }) => {
    const htmlContent = basicMarkdownToHTML(markdownText);

    return (
        <div
            className="markdown"
            dangerouslySetInnerHTML={{ __html: htmlContent }}
        />
    );
};

export default MarkdownRenderer;
