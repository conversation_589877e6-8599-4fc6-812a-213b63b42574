import React from 'react';
import MarkdownRenderer from './MarkdownRenderer';

const RenderAgentResponseBlock = ({ block, onAction, text }) => {
    console.log('RenderAgentResponseBlock :: block', block);
    switch (block?.type) {
        case 'markdown':
            return (
                <MarkdownRenderer
                    markdownText={block.content}
                ></MarkdownRenderer>
            );

        case 'actions':
            return (
                <div className="flex gap-4 mt-3">
                    {block.buttons.map((btn, i) => (
                        <button
                            key={i}
                            onClick={() => onAction(btn.value)}
                            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                        >
                            {btn.label}
                        </button>
                    ))}
                </div>
            );

        default:
            return text;
    }
};

export default RenderAgentResponseBlock;
