# Booking Cancellation Feature

## Overview
This feature handles automatic booking cancellation when a service provider is reassigned in the TMS system. When a user tries to reassign a Wify provider and there's already a booking, the system shows a confirmation modal and cancels all existing bookings upon confirmation.

## Frontend Implementation

### SrvcPrvdrSelector Component
- **File**: `frontend/react-app1/src/routes/services/SrvcPrvdrSelector.js`
- **Key Changes**:
  - Added booking confirmation modal when `isBooked` prop is true
  - Shows confirmation dialog: "Your previous booking with existing provider will be cancelled. Do you want to continue?"
  - Sends `is_prvdr_reassigned: true` parameter to backend when provider is changed

### Modal Flow
1. User selects a new provider when `isBooked` is true
2. Confirmation modal appears with warning message
3. User clicks "Save" to confirm or "Cancel" to abort
4. If confirmed, API call is made with `is_prvdr_reassigned: true`

## Backend Implementation

### Database Functions

#### tms_hlpr_delete_bookings_for_srvc_req
- **File**: `backend/App/dbFunctions/tms_hlpr_delete_bookings_for_srvc_req.sql`
- **Purpose**: Deletes all bookings for a service request and updates capacity
- **Process**:
  1. Gets capacity IDs that will be affected
  2. Deletes all bookings from `cl_tx_bookings` table
  3. Updates `cl_tx_capacity.booked_cap_in_minutes` to reflect freed slots
  4. Returns status and affected row counts

#### tms_hlpr_clear_booked_slots_from_form_data
- **File**: `backend/App/dbFunctions/tms_hlpr_clear_booked_slots_from_form_data.sql`
- **Purpose**: Removes booking-related fields from service request form_data
- **Parameters**:
  - `srvc_req_id_` (integer): Service request ID
  - `form_data_` (json): Form data containing user context and service type information
- **Process**:
  1. Extracts user context from form_data using `tms_get_user_context_from_data()`
  2. Builds JSON object to clear booking-related fields (booked_slots, start_time, end_time, etc.)
  3. Calls `tms_create_service_request()` to save the changes (following the same pattern as `tms_hlpr_update_srvc_req_vertical`)

### Service Request Creation Function
- **File**: `backend/App/dbFunctions/tms_create_service_request.sql`
- **Changes**:
  - Added booking cancellation logic when `is_prvdr_reassigned` is true
  - Calls helper functions to delete bookings and clear form data
  - Adds timeline entry for booking cancellation tracking

## Database Tables Affected

### cl_tx_bookings
- Bookings are deleted when provider is reassigned
- Foreign key relationship with `cl_tx_capacity` and `cl_tx_srvc_req`

### cl_tx_capacity
- `booked_cap_in_minutes` field is updated to reflect freed capacity
- Recalculated based on remaining bookings after deletion

### cl_tx_srvc_req
- `form_data.booked_slots` field is removed
- Timeline entry added for audit trail

## Testing

### Unit Tests
- **File**: `frontend/react-app1/src/routes/services/SrvcPrvdrSelector.test.js`
- **Test Case**: `shows booking cancellation confirmation when isBooked is true`
- **Coverage**:
  - Modal display when `isBooked` is true
  - Confirmation flow
  - API call with correct parameters

## Usage

### Props Required
```javascript
<SrvcPrvdrSelector
    isBooked={true} // Triggers booking cancellation flow
    urlToSubmit="/api/service-request/update"
    onDataModified={handleDataModified}
    // ... other props
/>
```

### API Parameters
When provider is reassigned with existing booking:
```javascript
{
    new_prvdr: 123,
    is_prvdr_reassigned: true,
    host_d: "localhost:3000"
}
```

## Migration
- **File**: `backend/App/migrations/359_booking_cancellation_functions.sql`
- Creates the helper functions for booking deletion and form data cleanup
- Includes proper documentation comments

## Timeline Tracking
The system automatically adds a timeline entry when bookings are cancelled:
- **Action**: "UPDATE"
- **Message**: "Previous booking cancelled due to provider reassignment"
- **Form Data**: Current form data for context
