Begin;
    ALTER TABLE public.cl_tx_srvc_req ADD IF NOT EXISTS capacity_id int NULL;
	ALTER TABLE public.cl_tx_srvc_req ADD IF NOT EXISTS booking_id int NULL;
	ALTER TABLE public.cl_tx_srvc_req ADD IF NOT EXISTS booking_details jsonb NULL;
   DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints
            WHERE constraint_name = 'cl_tx_srvc_req_capacity_id'
            AND table_schema = 'public'
            AND table_name = 'cl_tx_srvc_req'
        ) THEN
            ALTER TABLE public.cl_tx_srvc_req
            ADD CONSTRAINT cl_tx_srvc_req_capacity_id
            FOREIGN KEY (capacity_id)
            REFERENCES public.cl_tx_capacity(db_id);
        END IF;
        
    END
        $$;


END;