name: Stale Branch Detector

on:
    workflow_dispatch: # Allows manual execution from GitHub Actions UI
    # schedule:
    #     # Run nightly at 23:00 IST (17:30 UTC)
    #     - cron: '30 17 * * *'

# Cancel previous runs if new PR is merged
concurrency:
    group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
    cancel-in-progress: true

jobs:
    detect-stale-branches:
        name: Find Branches Older Than 15 Days
        runs-on: ubuntu-latest
        # Run when manually triggered OR on schedule
        if: github.event_name == 'workflow_dispatch' || github.event_name == 'schedule'
        outputs:
            has_stale_branches: ${{ steps.find-stale.outputs.has_stale_branches }}
            stale_count: ${{ steps.find-stale.outputs.stale_count }}

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0 # Fetch all branches and history

            - name: Find stale merged branches
              id: find-stale
              run: |
                  echo "🔍 Searching for merged branches older than 15 days..."
                  echo "📅 Current date: $(date)"

                  # Show trigger context
                  if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
                      echo "🚀 Triggered manually by: ${{ github.actor }}"
                  elif [ "${{ github.event_name }}" = "schedule" ]; then
                      echo "⏰ Triggered by nightly schedule (23:00 IST)"
                  fi
                  echo ""

                  # Get all remote branches
                  git fetch --all

                  # Calculate date 15 days ago
                  CUTOFF_DATE=$(date -d '15 days ago' '+%Y-%m-%d')
                  CUTOFF_TIMESTAMP=$(date -d '15 days ago' '+%s')

                  echo "📊 Cutoff date: $CUTOFF_DATE"
                  echo "🎯 Logic: Only checking branches that have been merged to main/master"
                  echo ""

                  # Determine the main branch name
                  MAIN_BRANCH=""
                  if git show-ref --verify --quiet refs/remotes/origin/main; then
                      MAIN_BRANCH="origin/main"
                      echo "📌 Main branch detected: main"
                  elif git show-ref --verify --quiet refs/remotes/origin/master; then
                      MAIN_BRANCH="origin/master"
                      echo "📌 Main branch detected: master"
                  else
                      echo "❌ Error: Could not find main or master branch"
                      exit 1
                  fi
                  echo ""

                  echo "🌿 Analyzing merged branches..."

                  STALE_BRANCHES=""
                  STALE_COUNT=0
                  TOTAL_MERGED_BRANCHES=0
                  ACTIVE_BRANCHES=0

                  # Get all remote branches except main/master
                  for branch in $(git branch -r --format='%(refname:short)' | grep -v 'HEAD' | grep -v "$MAIN_BRANCH" | sort); do
                      BRANCH_NAME=$(echo "$branch" | sed 's/origin\///')

                      # Skip if this is the main branch
                      if [ "$BRANCH_NAME" = "main" ] || [ "$BRANCH_NAME" = "master" ]; then
                          continue
                      fi

                      # Skip the protected branch doNotDelete/Jainish
                      if [ "$BRANCH_NAME" = "doNotDelete/Jainish" ]; then
                          echo "🔒 Skipping protected branch: $BRANCH_NAME"
                          continue
                      fi

                      # Check if this branch has been merged into main
                      MERGE_BASE=$(git merge-base "$branch" "$MAIN_BRANCH" 2>/dev/null || echo "")
                      BRANCH_HEAD=$(git rev-parse "$branch" 2>/dev/null || echo "")

                      if [ -n "$MERGE_BASE" ] && [ -n "$BRANCH_HEAD" ] && [ "$MERGE_BASE" = "$BRANCH_HEAD" ]; then
                          # Branch is fully merged
                          TOTAL_MERGED_BRANCHES=$((TOTAL_MERGED_BRANCHES + 1))

                          # Find when this branch was merged (look for merge commit in main)
                          MERGE_COMMIT=$(git log --merges --oneline --grep="$BRANCH_NAME" "$MAIN_BRANCH" --since="6 months ago" | head -1 | cut -d' ' -f1 2>/dev/null || echo "")

                          if [ -z "$MERGE_COMMIT" ]; then
                              # Fallback: use the last commit date of the branch
                              MERGE_DATE=$(git log -1 --format="%ci" "$branch" 2>/dev/null || echo "")
                          else
                              # Use the merge commit date
                              MERGE_DATE=$(git log -1 --format="%ci" "$MERGE_COMMIT" 2>/dev/null || echo "")
                          fi

                          if [ -n "$MERGE_DATE" ]; then
                              # Convert to timestamp for comparison
                              MERGE_TIMESTAMP=$(date -d "$MERGE_DATE" '+%s' 2>/dev/null || echo "0")

                              # Check if merge was more than 15 days ago
                              if [ "$MERGE_TIMESTAMP" -lt "$CUTOFF_TIMESTAMP" ]; then
                                  STALE_COUNT=$((STALE_COUNT + 1))
                                  DAYS_OLD=$(( ($(date '+%s') - MERGE_TIMESTAMP) / 86400 ))

                                  # Add to stale branches list
                                  if [ -z "$STALE_BRANCHES" ]; then
                                      STALE_BRANCHES="$BRANCH_NAME ($DAYS_OLD days since merge)"
                                  else
                                      STALE_BRANCHES="$STALE_BRANCHES\n$BRANCH_NAME ($DAYS_OLD days since merge)"
                                  fi
                              fi
                          fi
                      else
                          # Branch is not merged (still active)
                          ACTIVE_BRANCHES=$((ACTIVE_BRANCHES + 1))
                      fi
                  done

                  echo ""
                  if [ "$STALE_COUNT" -gt 0 ]; then
                      echo "🗑️  STALE BRANCHES (Safe to Delete):"
                      echo ""
                      echo -e "$STALE_BRANCHES"
                      echo ""
                      echo "💡 Delete command: git push origin --delete <branch-name>"

                      # Set output for potential use in other steps
                      echo "stale_count=$STALE_COUNT" >> $GITHUB_OUTPUT
                      echo "has_stale_branches=true" >> $GITHUB_OUTPUT

                      # Save stale branch names for deletion job
                      echo -e "$STALE_BRANCHES" | sed 's/ (.*//' > /tmp/stale_branches.txt
                      echo "stale_branches_file=/tmp/stale_branches.txt" >> $GITHUB_OUTPUT
                  else
                      echo "✅ No stale branches found - all good!"
                      echo "stale_count=0" >> $GITHUB_OUTPUT
                      echo "has_stale_branches=false" >> $GITHUB_OUTPUT
                      echo "stale_branches_file=" >> $GITHUB_OUTPUT
                  fi

            - name: Upload stale branches list
              if: steps.find-stale.outputs.has_stale_branches == 'true'
              uses: actions/upload-artifact@v4
              with:
                  name: stale-branches-list
                  path: /tmp/stale_branches.txt
                  retention-days: 1

            - name: No stale branches found
              if: steps.find-stale.outputs.has_stale_branches == 'false'
              run: |
                  echo "🎉 No cleanup needed - all branches are up to date!"

    delete-stale-branches:
        name: Delete Stale Branches
        runs-on: ubuntu-latest
        needs: detect-stale-branches
        if: needs.detect-stale-branches.outputs.has_stale_branches == 'true'

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0
                  token: ${{ secrets.GITHUB_TOKEN }}

            - name: Download stale branches list
              uses: actions/download-artifact@v4
              with:
                  name: stale-branches-list
                  path: /tmp/

            - name: Delete stale branches
              run: |
                  echo "🗑️ Starting deletion of stale branches..."
                  echo "📋 Branches to delete:"

                  DELETED_COUNT=0
                  FAILED_COUNT=0
                  DELETED_BRANCHES=""
                  FAILED_BRANCHES=""

                  while IFS= read -r branch_name; do
                      if [ -n "$branch_name" ]; then
                          echo "🔄 Attempting to delete: $branch_name"

                          # Check if branch exists on remote
                          if git ls-remote --heads origin "$branch_name" | grep -q "$branch_name"; then
                              # Attempt to delete the branch
                              if git push origin --delete "$branch_name" 2>/dev/null; then
                                  echo "✅ Successfully deleted: $branch_name"
                                  DELETED_COUNT=$((DELETED_COUNT + 1))
                                  if [ -z "$DELETED_BRANCHES" ]; then
                                      DELETED_BRANCHES="$branch_name"
                                  else
                                      DELETED_BRANCHES="$DELETED_BRANCHES, $branch_name"
                                  fi
                              else
                                  echo "❌ Failed to delete: $branch_name"
                                  FAILED_COUNT=$((FAILED_COUNT + 1))
                                  if [ -z "$FAILED_BRANCHES" ]; then
                                      FAILED_BRANCHES="$branch_name"
                                  else
                                      FAILED_BRANCHES="$FAILED_BRANCHES, $branch_name"
                                  fi
                              fi
                          else
                              echo "⚠️ Branch not found on remote: $branch_name (may have been deleted already)"
                          fi
                      fi
                  done < /tmp/stale_branches.txt

                  echo ""
                  echo "📊 DELETION SUMMARY:"
                  echo "✅ Successfully deleted: $DELETED_COUNT branches"
                  if [ "$DELETED_COUNT" -gt 0 ]; then
                      echo "   Deleted branches: $DELETED_BRANCHES"
                  fi

                  if [ "$FAILED_COUNT" -gt 0 ]; then
                      echo "❌ Failed to delete: $FAILED_COUNT branches"
                      echo "   Failed branches: $FAILED_BRANCHES"
                      echo ""
                      echo "💡 Failed deletions may be due to:"
                      echo "   - Branch protection rules"
                      echo "   - Insufficient permissions"
                      echo "   - Branch already deleted"
                  fi

                  echo ""
                  echo "🎉 Stale branch cleanup completed!"
