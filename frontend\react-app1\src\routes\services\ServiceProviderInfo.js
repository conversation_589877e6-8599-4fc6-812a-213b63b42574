import React from 'react';
import { Tooltip } from 'antd';

const ServiceProviderInfo = ({ items = [], showVerticalAndHub = true }) => {
    if (!items.length) return null;

    // Filter items based on feature flag
    const filteredItems = showVerticalAndHub 
        ? items 
        : items.filter(item => 
            item.label !== 'Vertical' && item.label !== 'Service Hub'
        );

    if (!filteredItems.length) return null;

    return (
        <div className="wy-srvc-prvdr-info-wrapper">
            {filteredItems.map(({ label, value }, index) => (
                <h6
                    key={index}
                    className="gx-mb-0 gx-text-grey gx-position-relative wy-srvc-prvdr-content-wrapper"
                >
                    <div
                        className={`wy-srvc-prvdr-${label.toLowerCase().replace(/\s+/g, '-')}-label`}
                    >
                        {label}
                    </div>
                    <div
                        className={`wy-srvc-prvdr-${label.toLowerCase().replace(/\s+/g, '-')}-value`}
                    >
                        <Tooltip title={value || 'N/A'}>
                            {value || 'N/A'}
                        </Tooltip>
                    </div>
                </h6>
            ))}
        </div>
    );
};

export default ServiceProviderInfo;
