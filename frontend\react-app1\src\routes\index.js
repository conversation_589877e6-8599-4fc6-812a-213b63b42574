import React from 'react';
import { Route, Switch } from 'react-router-dom';

import asyncComponent from 'util/asyncComponent';

const App = ({ match }) => (
    <div className="gx-main-content-wrapper">
        <Switch>
            <Route
                path={`${match.url}sample`}
                component={asyncComponent(() => import('./SamplePage'))}
            />
            <Route
                path={`${match.url}devlPlayground`}
                component={asyncComponent(() => import('./devl-playground'))}
            />
            <Route
                path={`${match.url}setup/user-config/roles`}
                component={asyncComponent(
                    () => import('./setup/user-config/roles/index')
                )}
            />
            <Route
                path={`${match.url}setup/srvc-req/types`}
                component={asyncComponent(
                    () => import('./setup/srvc-req/service-types')
                )}
            />
            <Route
                path={`${match.url}setup/srvc-req/sub-tasks-types`}
                component={asyncComponent(
                    () => import('./setup/srvc-req/sub-tasks-types')
                )}
            />
            <Route
                path={`${match.url}crud-overview-demo`}
                component={asyncComponent(
                    () =>
                        import(
                            '../components/wify-utils/crud/overview/DemoOverview'
                        )
                )}
            />
            <Route
                path={`${match.url}users`}
                component={asyncComponent(() => import('./users'))}
            />
            <Route
                path={`${match.url}srvcProviders`}
                component={asyncComponent(() => import('./srvcProviders'))}
            />
            <Route
                path={`${match.url}deployment-requests/:srvc_type_id?/:showDeploymentReq?/:selected_deployment_date?`}
                component={asyncComponent(
                    () => import('./deployment-requests/index.js')
                )}
            />

            <Route
                path={`${match.url}dashboard`}
                component={asyncComponent(
                    () => import('./dashboard/CustAdminDashboard')
                )}
            />
            <Route
                path={`${match.url}customer`}
                component={asyncComponent(() => import('./customer'))}
            />
            <Route
                path={`${match.url}toggle-role`}
                component={asyncComponent(() => import('./switchrole'))}
            />
            <Route
                path={`${match.url}services/:srvc_id`}
                component={asyncComponent(() => import('./services'))}
            />
            <Route
                path={`${match.url}customer-requests`}
                component={asyncComponent(() => import('./services'))}
            />
            <Route
                path={`${match.url}my-tasks`}
                component={asyncComponent(() => import('./my-tasks'))}
            />
            <Route
                path={`${match.url}setup/user-config/locations`}
                component={asyncComponent(
                    () => import('./setup/user-config/locations/index')
                )}
            />
            <Route
                path={`${match.url}dashboard-user`}
                component={asyncComponent(
                    () => import('./dashboard/CustUserDashboard')
                )}
            />
            <Route
                path={`${match.url}setup/insights/visitMap`}
                component={asyncComponent(() => import('./insights/visitMap'))}
            />
            <Route
                path={`${match.url}setup/insights/siteMap`}
                component={asyncComponent(() => import('./insights/siteMap'))}
            />
            <Route
                path={`${match.url}setup/insights/serviceMetrics`}
                component={asyncComponent(
                    () => import('./insights/serviceMetrics')
                )}
            />
            <Route
                path={`${match.url}setup/insights/projects/:vertical?`}
                component={asyncComponent(
                    () => import('./services/project/ProjectsAccess')
                )}
            />
            <Route
                path={`${match.url}dashboard-prvdr`}
                component={asyncComponent(
                    () => import('./dashboard/PrvdrUserDashboard')
                )}
            />

            <Route
                path={`${match.url}dashboard-onfield`}
                component={asyncComponent(
                    () => import('./dashboard/OnFieldUserHome')
                )}
            />
            <Route
                path={`${match.url}my-availability`}
                component={asyncComponent(
                    () => import('./ace-my-availability')
                )}
            />
            <Route
                path={`${match.url}customer-access/:cust_org_id?/:cust_srvc_id?`}
                component={asyncComponent(
                    () => import('./services/CustomerAccess')
                )}
            />
            <Route
                path={`${match.url}api-docs`}
                component={asyncComponent(() => import('./api-docs'))}
            />
            <Route
                path={`${match.url}setup/srvc-req/status-group`}
                component={asyncComponent(
                    () => import('./setup/srvc-req/status-group')
                )}
            />
            <Route
                path={`${match.url}warehouse/12`}
                component={asyncComponent(() => import('./warehouse-proto'))}
            />
            <Route
                path={`${match.url}tutorials`}
                component={asyncComponent(() => import('./tutorials'))}
            />

            <Route
                path={`${match.url}technician-app-access/:role_id?/:technician_id?`}
                component={asyncComponent(
                    () => import('./technician-app-access')
                )}
            />
            <Route
                path={`${match.url}setup/attendance/leaves`}
                component={asyncComponent(
                    () => import('./attendance/availability')
                )}
            />
            <Route
                path={`${match.url}setup/attendance/assignment`}
                component={asyncComponent(
                    () => import('./attendance/assignment')
                )}
            />
            <Route
                path={`${match.url}setup/attendance/attendance`}
                component={asyncComponent(
                    () => import('./attendance/attendance')
                )}
            />
            <Route
                path={`${match.url}my-leaves`}
                component={asyncComponent(
                    () => import('./attendance/availability')
                )}
            />

            <Route
                path={`${match.url}version-log`}
                component={asyncComponent(() => import('./versionLog'))}
            />
            <Route
                path={`${match.url}setup/user-config/fields`}
                component={asyncComponent(
                    () => import('./setup/user-config/custom-fields/index')
                )}
            />
            <Route
                path={`${match.url}setup/attendance/office_attendance`}
                component={asyncComponent(
                    () => import('./attendance/office_attendance')
                )}
            />
            <Route
                path={`${match.url}setup/srvc-req/service-provider-fields`}
                component={asyncComponent(
                    () => import('./setup/srvc-req/sp-fields/index')
                )}
            />
            {/* <Route path={`${match.url}setup/attendance/report`} component={asyncComponent(() => import('./attendance/report'))}/> */}
            <Route
                path={`${match.url}setup/attendance/daily_report`}
                component={asyncComponent(
                    () => import('./attendance/daily_report')
                )}
            />
            <Route
                path={`${match.url}setup/attendance/range-report`}
                component={asyncComponent(
                    () => import('./attendance/range_report')
                )}
            />
            <Route
                path={`${match.url}setup/srvc-req/service-provider-authorities`}
                component={asyncComponent(
                    () => import('./setup/srvc-req/srvc-prvdr-authorities')
                )}
            />
            <Route
                path={`${match.url}organisations`}
                component={asyncComponent(() => import('./organisations'))}
            />
            <Route
                path={`${match.url}dashboard-owner`}
                component={asyncComponent(
                    () => import('./dashboard/owner-dashboard')
                )}
            />
            <Route
                path={`${match.url}bulk-actions`}
                component={asyncComponent(
                    () => import('./ow-bulk-actions/index.tsx')
                )}
            />
            <Route
                path={`${match.url}my-profile`}
                component={asyncComponent(() => import('../routes/my-profile'))}
            />
            <Route
                path={`${match.url}main/my-account/my-profile`}
                component={asyncComponent(() => import('../routes/my-profile'))}
            />
            <Route
                path={`${match.url}main/my-account/my-settings`}
                component={asyncComponent(
                    () => import('./my-settings/index.js')
                )}
            />
            <Route
                path={`${match.url}ow-setup/configuration/create-service-types`}
                component={asyncComponent(
                    () => import('../routes/ow-setup/add-st')
                )}
            />
            <Route
                path={`${match.url}ow-setup/configuration/manage-subtask-types`}
                component={asyncComponent(
                    () => import('../routes/ow-setup/manage-sbtask')
                )}
            />
            <Route
                path={`${match.url}ow-setup/configuration/manage-feature-flags`}
                component={asyncComponent(
                    () => import('../routes/ow-setup/manage-feature-flags')
                )}
            />
            <Route
                path={`${match.url}setup/automation-deployment/pool-view`}
                component={asyncComponent(
                    () =>
                        import('./setup/automation-deployment/pool-view/index')
                )}
            />
            <Route
                path={`${match.url}setup/automation-deployment/pulse-tracker`}
                component={asyncComponent(
                    () =>
                        import(
                            './setup/automation-deployment/pulse-tracker/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}setup/automation-deployment/lambda-based`}
                component={asyncComponent(
                    () =>
                        import(
                            './setup/automation-deployment/lambda-based/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}setup/automation-deployment/job-broadcast`}
                component={asyncComponent(
                    () =>
                        import(
                            './setup/automation-deployment/job-broadcast/index.js'
                        )
                )}
            />

            <Route
                path={`${match.url}rating/rating-templates`}
                component={asyncComponent(
                    () => import('../routes/rating/rating-templates')
                )}
            />
            <Route
                path={`${match.url}setup/rate-config/billing-fields`}
                component={asyncComponent(
                    () => import('./setup/rate-config/billing-fields/index')
                )}
            />

            <Route
                path={`${match.url}setup/rate-config/rate-card/:vertical?/:specific_select_field?`}
                component={asyncComponent(
                    () => import('./setup/rate-config/rate-card/index')
                )}
            />

            <Route
                path={`${match.url}setup/capacity/service-hubs/:vertical_id?`}
                component={asyncComponent(
                    () => import('./setup/capacity/service-hubs/index')
                )}
            />

            <Route
                path={`${match.url}setup/capacity/availability-slots`}
                component={asyncComponent(
                    () => import('./setup/capacity/availability-slots/index')
                )}
            />
            <Route
                path={`${match.url}setup/capacity/time-slots/:vertical_id?`}
                component={asyncComponent(
                    () => import('./setup/capacity/time-slots/index')
                )}
            />

            <Route
                path={`${match.url}setup/user-config/restrictions`}
                component={asyncComponent(
                    () => import('./setup/user-config/restriction/index.js')
                )}
            />
            <Route
                path={`${match.url}change-log`}
                component={asyncComponent(() => import('./change-log'))}
            />
            <Route
                path={`${match.url}setup/insights/taskUpdates/:sbtsk_type_id?`}
                component={asyncComponent(
                    () => import('./insights/taskUpdates')
                )}
            />
            <Route
                path={`${match.url}setup/srvc-req/settings`}
                component={asyncComponent(
                    () => import('./setup/srvc-req/settings/index.js')
                )}
            />
            <Route
                path={`${match.url}main/ace/quick-assignment`}
                component={asyncComponent(
                    () => import('./quick-assignment/index.js')
                )}
            />
            <Route
                path={`${match.url}main/ace/auto-assign`}
                component={asyncComponent(
                    () => import('./auto-assign/index.js')
                )}
            />
            <Route
                path={`${match.url}main/ace/exceptions`}
                component={asyncComponent(
                    () => import('./ace-exceptions-dashboard/index.js')
                )}
            />
            <Route
                path={`${match.url}main/ace/runs`}
                component={asyncComponent(
                    () => import('./ace-runs-dashboard/index.js')
                )}
            />
            <Route
                path={`${match.url}main/ace/capacity-dashboard/:vertical_id?`}
                component={asyncComponent(
                    () => import('./ace/ace-capacity-dashboard')
                )}
            />
            <Route
                path={`${match.url}job-broadcasts`}
                component={asyncComponent(
                    () => import('./ace-job-broadcasts/index.js')
                )}
            />

            <Route
                path={`${match.url}setup/ratings/task-wise`}
                component={asyncComponent(
                    () => import('./all-ratings/index.js')
                )}
            />
            <Route
                path={`${match.url}main/manage-inventory/manage-skus`}
                component={asyncComponent(
                    () =>
                        import(
                            './parts-management/manage-inventory/manage-SKUs/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}main/manage-inventory/manage-spares`}
                component={asyncComponent(
                    () =>
                        import(
                            './parts-management/manage-inventory/manage-spares/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}main/manage-inventory/manage-warehouse`}
                component={asyncComponent(
                    () =>
                        import(
                            './parts-management/manage-inventory/manage-warehouse/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}main/manage-inventory/manage-stock/:stock_type?`}
                component={asyncComponent(
                    () =>
                        import(
                            './parts-management/manage-inventory/manage-stocks/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}main/stock-transfer/to-sp/:trsnfr_frm_warehouse?/:prvdr_list?/:sp_warehouse?/:inventory_type?`}
                component={asyncComponent(
                    () =>
                        import(
                            './parts-management/manage-inventory/stock-transfer/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}setup/inventory-config/manage-custom-fields`}
                component={asyncComponent(
                    () =>
                        import(
                            './setup/inventory-config/manage-custom-fields/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}main/stock-transfer/transfer-history`}
                component={asyncComponent(
                    () =>
                        import(
                            './parts-management/manage-inventory/stock-transfer-history/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}main/stock-transfer/incoming-transfer`}
                component={asyncComponent(
                    () =>
                        import(
                            './parts-management/manage-inventory/incoming-transfer/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}main/manage-inventory/sp-inventory-master`}
                component={asyncComponent(
                    () =>
                        import(
                            './parts-management/manage-inventory/sp-inventory-master/index.js'
                        )
                )}
            />

            <Route
                path={`${match.url}setup/user-config/zones`}
                component={asyncComponent(
                    () => import('./setup/user-config/zones/index')
                )}
            />

            <Route
                path={`${match.url}main/stock-transfer/to-technician/:on_field_user_list?/:brand_list?/:sp_warehouse?/:inventory_type?`}
                component={asyncComponent(
                    () =>
                        import(
                            './parts-management/manage-inventory/transfer-to-technician/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}main/inventory/my-inventory`}
                component={asyncComponent(
                    () =>
                        import(
                            './parts-management/inventory/my-inventory/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}setup/ratings/day-wise`}
                component={asyncComponent(
                    () => import('./all-ratings/grouped-ratings/index.js')
                )}
            />
            <Route
                path={`${match.url}setup/capacity/vertical-skills/:vertical_id?`}
                component={asyncComponent(
                    () => import('./setup/capacity/vertical-skills')
                )}
            />
            <Route
                path={`${match.url}setup/capacity/capacity-settings`}
                component={asyncComponent(
                    () => import('./setup/capacity/capacity-settings/index.tsx')
                )}
            />
            <Route
                path={`${match.url}main/stock-transfer/inter-warehouse/:trsnfr_frm_warehouse?/:trsnfr_to_warehouse?/:inventory_type?`}
                component={asyncComponent(
                    () =>
                        import(
                            './parts-management/manage-inventory/inter-warehouse/index.js'
                        )
                )}
            />
            <Route
                path={`${match.url}setup/attendance/availability-report`}
                component={asyncComponent(
                    () => import('./attendance/user-availability-report')
                )}
            />
        </Switch>
    </div>
);

export default App;
