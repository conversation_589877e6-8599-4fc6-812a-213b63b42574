import React, { useState, useEffect } from 'react';
import { Form, Button, message, Upload, Card, Alert, Collapse } from 'antd';
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons';
import FormBuilder from 'antd-form-builder';
import http_utils from '../../util/http_utils';
import CircularProgress from '../../components/CircularProgress';
import BulkUploader from '../../components/wify-utils/BulkUploader';

const submitUrl = '/bulk-update/service-request-status';

const ServiceReqStatusUpdate = () => {
    const getFieldsMetaFrBulkUpload = () => {
        return [
            {
                label: 'TMS ID',
                key: 'tms_id',
                required: true,
                widget: 'input',
                widgetProps: {
                    placeholder: 'Enter TMS ID',
                },
            },
            {
                label: 'New Status',
                key: 'new_status',
                required: true,
                widget: 'input',
                widgetProps: {
                    placeholder: 'Enter New Status',
                },
            },
            {
                label: 'Comment',
                key: 'comment',
                required: true,
                widget: 'input',
                widgetProps: {
                    placeholder: 'Enter Comment',
                },
            },
            {
                label: 'Provider ID',
                key: 'provider_id',
                required: false,
                widget: 'number',
                widgetProps: {
                    placeholder: 'Enter Provider ID',
                },
                rules: [
                    {
                        validator: ({ getFieldValue }) => {
                            const provider_id = getFieldValue('provider_id');
                            const service_type_id =
                                getFieldValue('service_type_id');
                            if (!provider_id && !service_type_id) {
                                return Promise.reject(
                                    new Error(
                                        'Either Provider ID or Service Type ID is required'
                                    )
                                );
                            }
                            if (provider_id && service_type_id) {
                                return Promise.reject(
                                    new Error(
                                        'Please provide either Provider ID or Service Type ID, not both.'
                                    )
                                );
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                label: 'Service Type ID',
                key: 'service_type_id',
                required: false,
                widget: 'number',
                widgetProps: {
                    placeholder: 'Enter Service Type ID',
                },
            },
        ];
    };

    return (
        <div>
            <Collapse>
                <Collapse.Panel
                    header={
                        <span className="gx-text-primary">
                            <UploadOutlined className="gx-mr-2" />
                            Click here for Bulk updation
                        </span>
                    }
                >
                    <Alert
                        showIcon
                        type="info"
                        message="Bulk updates are processed in the background. You will receive an email notification once the operation is complete."
                    />
                    <BulkUploader
                        onDataModified={(entry_id) => {}}
                        submitUrl={submitUrl}
                        dataProto={getFieldsMetaFrBulkUpload()}
                    />
                </Collapse.Panel>
            </Collapse>
        </div>
    );
};

export default ServiceReqStatusUpdate;
