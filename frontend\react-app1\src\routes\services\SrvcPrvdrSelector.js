import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Button, Form, Select, Avatar, Image, message, Modal } from 'antd';
import { PlusOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import CircularProgress from '../../components/CircularProgress';
import http_utils from '../../util/http_utils';
import { convertUTCToDisplayTime } from '../../util/helpers';
import ConfigHelpers from '../../util/ConfigHelpers';
import ServiceProviderInfo from './ServiceProviderInfo';

const { Option } = Select;

class SrvcPrvdrSelector extends Component {
    constructor(props) {
        super(props);
        this.state = {
            activeFilters: {},
            drawerState: false,
            showItemEditor: false,
            isLoadingViewData: false,
            viewData: undefined,
            error: '',
            showForm: false,
            currentPrvdr: this.getPrvdrByKey(this.props.currentSrvcPrvdr),
            canReassignPrvdr: this.props.isSpReassignmentAllowed,
        };
    }

    getPrvdrByKey(currentSrvcPrvdrKey) {
        return this.props.allSrvcPrvdrs.filter(
            (singlePrvdr) => singlePrvdr.value == currentSrvcPrvdrKey
        )[0];
    }

    handleOnSelectClick() {
        this.setState({
            showForm: !this.state.showForm,
        });
    }

    submitForm = (data) => {
        // Check if booking exists and show confirmation
        if (this.props.isBooked) {
            Modal.confirm({
                title: 'Confirm Provider Change',
                icon: <ExclamationCircleOutlined />,
                content:
                    'Your previous booking with existing provider will be cancelled. Do you want to continue?',
                okText: 'Save',
                cancelText: 'Cancel',
                onOk: () => {
                    this.performSubmit(data);
                },
                onCancel: () => {
                    // Do nothing, just close the modal
                },
            });
        } else {
            this.performSubmit(data);
        }
    };

    performSubmit = (data) => {
        this.setState({
            isFormSubmitting: true,
        });

        var params = data;
        if (data.new_prvdr != this.state.currentPrvdr?.value) {
            params['is_prvdr_reassigned'] = true;
        }
        params['host_d'] = window.location.host;
        const onComplete = (resp) => {
            this.setState({
                isFormSubmitting: false,
                error: '',
                showForm: false,
                currentPrvdr: this.getPrvdrByKey(params.new_prvdr),
            });
            this.tellParentToRefreshList(resp.entry_id);
        };
        const onError = (error) => {
            // compare statuses here
            this.setState({
                isFormSubmitting: false,
                error: http_utils.decodeErrorToMessage(error),
            });
        };
        http_utils.performPutCall(
            this.props.urlToSubmit,
            params,
            onComplete,
            onError
        );
    };

    tellParentToRefreshList(entry_id) {
        // console.log("Trying to to tell parent to refresh list");
        if (this.props.onDataModified != undefined) {
            this.props.onDataModified(entry_id);
        }
    }

    render() {
        console.log('SrvcPrvdrSelector props', this.props);
        const { currentPrvdr, isFormSubmitting, error, showForm } = this.state;
        const { allowedSrvcPrvdrs, allSrvcPrvdrs, currentPrvdrAssgTimestamp } =
            this.props;
        const srvcPrvdOptions = allSrvcPrvdrs?.filter(
            (prvdr) =>
                allowedSrvcPrvdrs?.includes(prvdr?.value) &&
                prvdr?.value != currentPrvdr?.value
        );
        // console.log('allowedSrvcPrvdrs',allowedSrvcPrvdrs);
        return (
            <>
                <div className="gx-card gx-border gx-px-2 gx-py-2 gx-mb-3">
                    {/* <Select  
                        style={{ width: '100%' }}>
                        <Select.Option
                            >
                            <span className="">INSTALLCO WIFY TECHNOLOGY PVT. LTD</span>
                        </Select.Option>
                    </Select> */}
                    <div className="gx-media gx-flex-nowrap ">
                        <div className="gx-mr-3 gx-pt-2">
                            <i
                                className={`icon icon-company gx-fs-xlxl gx-text-orange`}
                            />
                            {/* <img 
                                style={{maxWidth:'100px'}}
                                src="https:home.wify.co.in/assets/logo/wify_logo.png" /> */}
                        </div>
                        <div className="gx-media-body">
                            <h6 className="gx-mb-1 gx-text-grey">
                                Service provider
                            </h6>
                            {currentPrvdr && (
                                <>
                                    <p className="gx-mb-1 gx-text-primary">
                                        {currentPrvdr.full_name}
                                    </p>
                                    {currentPrvdrAssgTimestamp && (
                                        <h6 className="gx-mb-0 gx-text-grey">
                                            {currentPrvdrAssgTimestamp &&
                                                `Assigned ${convertUTCToDisplayTime(currentPrvdrAssgTimestamp)}`}
                                        </h6>
                                    )}
                                </>
                            )}
                            {(!currentPrvdr || this.state.canReassignPrvdr) &&
                                !ConfigHelpers.isServiceProvider() && (
                                    <Button
                                        type="link"
                                        className="gx-mb-0 gx-pl-0"
                                        icon={<PlusOutlined />}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            this.handleOnSelectClick();
                                        }}
                                    >
                                        ASSIGN
                                    </Button>
                                )}
                            {showForm && (
                                <Form
                                    onFinish={(data) => {
                                        this.submitForm(data);
                                    }}
                                    layout="vertical"
                                >
                                    <Form.Item
                                        label="Select provider"
                                        name="new_prvdr"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                        }}
                                    >
                                        <Select
                                            style={{
                                                width: '100%',
                                            }}
                                        >
                                            {srvcPrvdOptions.map(
                                                (singleOption, index) =>
                                                    singleOption.value !=
                                                        -1 && (
                                                        <Option
                                                            key={index}
                                                            value={
                                                                singleOption.value
                                                            }
                                                        >
                                                            <Avatar
                                                                src={
                                                                    <Image
                                                                        preview={
                                                                            false
                                                                        }
                                                                        src={
                                                                            singleOption.icon_path
                                                                        }
                                                                    />
                                                                }
                                                            />

                                                            <span className="gx-ml-3">
                                                                {
                                                                    singleOption.label
                                                                }
                                                            </span>
                                                        </Option>
                                                    )
                                            )}
                                        </Select>
                                    </Form.Item>

                                    <Form.Item>
                                        <Button
                                            type="primary"
                                            htmlType="submit"
                                            disabled={isFormSubmitting}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                            }}
                                        >
                                            Save
                                        </Button>
                                        {this.state.canReassignPrvdr ? (
                                            <p>
                                                **Reassignment can be done until
                                                there are no SP subtasks
                                            </p>
                                        ) : (
                                            <p>
                                                **Once selected cannot be
                                                changed
                                            </p>
                                        )}
                                    </Form.Item>
                                    {isFormSubmitting ? (
                                        <div className="gx-loader-view gx-loader-position">
                                            <CircularProgress />
                                        </div>
                                    ) : null}
                                    {error ? (
                                        <p className="gx-text-red">{error}</p>
                                    ) : null}
                                </Form>
                            )}
                        </div>
                    </div>
                    {this.props.TMS250623627177 && this.props.isSrvcPrvdr && (
                        <ServiceProviderInfo
                            items={[
                                {
                                    label: 'Vertical',
                                    value: this.props.sp_mappings
                                        ?.vertical_details?.label,
                                },
                                {
                                    label: 'Service Hub',
                                    value: this.props.sp_mappings?.service_hub
                                        ?.label,
                                },
                            ]}
                            showVerticalAndHub={this.props.showVerticalAndHub}
                        />
                    )}
                </div>
            </>
        );
    }
}

SrvcPrvdrSelector.propTypes = {};

export default SrvcPrvdrSelector;
