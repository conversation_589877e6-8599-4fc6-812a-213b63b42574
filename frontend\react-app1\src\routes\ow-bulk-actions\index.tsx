import React from 'react';
import { Tabs, Card } from 'antd';
import { Redirect } from 'react-router-dom';
import IntlMessages from '../../util/IntlMessages';
import ConfigHelpers from '../../util/ConfigHelpers';
import ServiceReqStatusUpdate from './ServiceReqStatusUpdate';

const { TabPane } = Tabs;

const BulkActions: React.FC = () => {
    if (!ConfigHelpers.isOwner()) {
        return <Redirect to="/dashboard" />;
    }

    return (
        <div className="gx-main-content">
            <div className="gx-module-box-content-section">
                <Tabs
                    defaultActiveKey="status-update"
                    type="line"
                    tabBarGutter={24}
                >
                    <TabPane
                        tab={
                            <IntlMessages
                                id="bulk.serviceReqStatusUpdate"
                                defaultMessage="Service Req Status Update"
                            />
                        }
                        key="status-update"
                    >
                        <ServiceReqStatusUpdate />
                    </TabPane>
                </Tabs>
            </div>
        </div>
    );
};

export default BulkActions;
