CREATE OR REPLACE FUNCTION public.tms_hlpr_clear_booked_slots_from_form_data(srvc_req_id_ integer, form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare
    -- Bare minimums
    status boolean;
    message text;
    resp_data json;

    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    srvc_type_id_ integer;

    -- For processing
    user_context_json json;
    booking_clear_json json;

begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';

    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    srvc_type_id_ := (form_data_->>'srvc_type_id')::integer;

    -- Get user context JSON
    user_context_json = tms_get_user_context_from_data(form_data_);
    user_context_json = jsonb_set(user_context_json::jsonb,'{srvc_type_id}',to_jsonb(srvc_type_id_),true);

    -- Prepare the booking clear JSON to remove booking-related fields
    booking_clear_json = jsonb_set(user_context_json::jsonb, '{booked_slots}', 'null'::jsonb, true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{start_time}', 'null'::jsonb, true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{end_time}', 'null'::jsonb, true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{request_req_date}', 'null'::jsonb, true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{booking_data}', 'null'::jsonb, true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{capacity_id}', 'null'::jsonb, true);
    booking_clear_json = jsonb_set(booking_clear_json::jsonb, '{booking_details}', 'null'::jsonb, true);

    -- Call tms_create_service_request to save the changes
    perform tms_create_service_request(booking_clear_json, srvc_req_id_);

    status = true;
    message = 'success';
    resp_data = json_build_object(
        'srvc_req_id', srvc_req_id_,
        'booked_slots_cleared', true
    );

    return json_build_object('status', status, 'code', message, 'data', resp_data);
END;
$function$
;
