var sampleOperationResp = require('../api_models/utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../api_models/utils/db_resp');
const pagination_filters_utils = require('../api_models/utils/pagination_filters_utils');
const commonUtils = require('../api_models/utils/common');
const users_model = require('./users_model');
// const { default: services_workflow } = require("./workflows/services_workflow");
const JSONStream = require('JSONStream');
const jsonToCsv = require('json-to-csv-stream');
const fs = require('fs');
const path = require('path');
const nodemailer = require('nodemailer');
const geo_coding_utils = require('./utils/geo_coding_utils');
const { doProactiveRequestCreation } = require('./queues/queues');
const { resolve } = require('path');
const { getUserContextFrmReq } = require('./utils/authrizor');
const ProactiveLogger = require('./loggers/ProactiveLogger');
const app_call = require('../api_models/utils/app_call');
const subtasks_model = require('./subtasks_model');
const {
    setParamsToServicesModel,
    setParamsToSubtaskModel,
} = require('./queues/processors/helpers');
const { allQueues } = require('./queues_v2/queues');
const {
    specificFieldsAuthoritiesNotificationEmailTemplate,
} = require('./queues_v2/processors/email/templates/specific_fields_authorities_notification_template');
const {
    discountApprovalAuthoritiesNotificationTemplate,
} = require('./queues_v2/processors/email/templates/discount_approval_authorities_notification_template');
const {
    discountApprovalStatusChangeNotificationTemplate,
} = require('./queues_v2/processors/email/templates/discount_approval_status_change_notification_template');
const {
    SrvcReqSendForBillingNotificationTemplate,
} = require('./queues_v2/processors/email/templates/srvc_req_send_for_billing_notification_temp');
const { callLambdaFn } = require('./utils/lambda_helpers');
const {
    getServiceWorkflowModel,
} = require('./queues_v2/processors/helpers/services_workflow_helper');

const {
    dumpExportReqCounter,
    dumpExportFailureCounter,
    dumpExportSuccessCounter,
    dumpExportStatus,
    dumpExportsCounter,
} = require('./utils/metrics');
const {
    get5MonthsDateRangeFilter,
    moduleKeys,
    parseFormulaToString,
    hasDataChanged,
    parseFormulaToValue,
    getIdVsLabelMapping,
} = require('./utils/helper');
const Daily_max_calls_allowed = 3;
const numOr0 = (n) => (!n || isNaN(n) ? 0 : n);
const { v4: uuidv4 } = require('uuid');
const moment = require('moment');

class services_model {
    async performPeriodicAutomation(jobData) {
        this.srvcTypeId = jobData.srvc_type_id;
        const dbObj = this.dbReplica || this.db;
        if (this.dbReplica) {
            // console.log('Loading data from Replica');
        }
        const _userData = {
            ip_address: 'localhost',
            user_agent: 'System-Agent',
            org_id: jobData.org_id,
        };
        // console.log('gai :: subtask_rating_model :: updateSubtaskGaiRatingData :: _userData :: ', _userData);
        const userFormData = JSON.stringify(_userData);
        // console.log('gai :: subtask_rating_model :: updateSubtaskGaiRatingData :: userFormData :: ', userFormData);
        const userRes =
            await this.db.tms_get_or_create_system_usr(userFormData);
        const user_id = userRes[0]?.tms_get_or_create_system_usr?.data?.usr_id;

        let dummy_req_for_user_context = {
            uuid: user_id,
            user_details: {
                org: {
                    id: jobData.org_id,
                },
            },
        };

        this.user_context = getUserContextFrmReq(dummy_req_for_user_context);
        this.userContext = getUserContextFrmReq(dummy_req_for_user_context);
        // this.srvc_type_id = proactive_srvc_type_id;
        let query = {};
        query['org_id'] = users_model.getOrgId(this.userContext);
        query['usr_id'] = users_model.getUUID(this.userContext);
        query = {
            ...query,
            ...jobData, // srvc_type_id coming from this
        };
        // console.log('Query', JSON.stringify(query));
        // Get all service requests in the status
        let srvcReqs = (
            await dbObj.tms_get_srvc_reqs_fr_periodic_automation(
                JSON.stringify(query)
            )
        )[0].tms_get_srvc_reqs_fr_periodic_automation;
        // console.log('srvcReqs', srvcReqs);

        let lambdaARN = jobData.lambda_arn;
        const params = {
            FunctionName: lambdaARN,
            InvocationType: 'RequestResponse',
            LogType: 'Tail',
            Payload: JSON.stringify({ ...srvcReqs.data }),
        };
        console.log('performPeriodicAutomation calling lambda', params.Payload);
        try {
            let respData = await callLambdaFn(params);
            // console.log(respData, 'respData');
            let lambdaRespData = JSON.parse(respData.Payload);

            if (lambdaRespData && lambdaRespData.statusCode) {
                console.log(
                    'performPeriodicAutomation lambdaRespData.data ::',
                    lambdaRespData.data
                );
                const { requestsToBeUpdated } = lambdaRespData.data;
                let updateReqsQuery = {};
                if (requestsToBeUpdated) {
                    updateReqsQuery['org_id'] = users_model.getOrgId(
                        this.userContext
                    );
                    updateReqsQuery['usr_id'] = users_model.getUUID(
                        this.userContext
                    );
                    updateReqsQuery = {
                        ...updateReqsQuery,
                        ..._userData,
                    };
                    const batch_data = [];
                    requestsToBeUpdated.forEach((singleEntry) => {
                        batch_data.push({
                            ...singleEntry,
                            srvc_req_id: singleEntry.db_id,
                            host_d: 'tms.wify.co.in', // Should ideally come from ENV
                            is_frm_frontend: true,
                        });
                    });
                    updateReqsQuery['batch_data'] = batch_data;
                } else {
                    return;
                }
                let resp = await this.createOrUpdateBatch(
                    updateReqsQuery,
                    0,
                    1
                );
                console.log('performPeriodicAutomation updation resp::', resp);
            } else {
                console.log(
                    'performPeriodicAutomation lambda failed',
                    lambdaRespData
                );
            }
        } catch (error) {
            console.log('performPeriodicAutomation error msg::', error);
        }
    }

    getSrvcReqsFrGenAutoAssign(query) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['pagination_data'] =
                    pagination_filters_utils.decodeQueryParams(query, true);
                query['srvc_type_id'] = 0;
                query['is_frontend'] = true;
                // console.log('tms_get_srvc_reqs_fr_gen_auto_assgn data',JSON.stringify(query))
                let srvcReqs = (
                    await this.db.tms_get_srvc_reqs_fr_gen_auto_assgn(
                        JSON.stringify(query)
                    )
                )[0].tms_get_srvc_reqs_fr_gen_auto_assgn;
                // console.log('srvcReqs',JSON.stringify(srvcReqs));
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify(srvcReqs.data),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error('getSrvcReqsFrGenAutoAssign', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        'Unable to find requests',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    updateMissingGeoLoc(new_entry_id) {
        return new Promise(async (resolve, reject) => {
            try {
                await this.syncSrvcReqLocWithCache(new_entry_id);
            } catch (rejectedResp) {
                let query = {};
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                // start a bull job
                // to hit Google Api and then internally it will resync cache
                // console.log("service syncSrvcReqLocWithCache in rejectedResp..",rejectedResp);
                let jobData = {
                    form_data: rejectedResp.form_data,
                    query,
                    new_entry_id,
                    services_model_data: this.getServicesModelData(this),
                };
                allQueues.WIFY_SRVC_REQ_LOC_MAPPING.addJob(jobData);
            }
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    getSrvcTypeConfigData(query) {
        return new Promise((resolve, reject) => {
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['srvc_type_id'] = this.srvcTypeId || query?.srvc_type_id;
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['filters'] = filters_;
            var form_data = JSON.stringify(query);
            // console.log("getSrvcTypeConfigData ==>",form_data);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_hlpr_get_config_data_for_srvc_type(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_hlpr_get_config_data_for_srvc_type
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getSrvcTypeOrVerticalTypeConfigData(query) {
        return new Promise((resolve, reject) => {
            query['srvc_type_id'] = this.srvcTypeId || query?.srvc_type_id;
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);

            const form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            this.db
                .tms_hlpr_get_config_data_for_srvc_type_or_vertical(form_data)
                .then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_hlpr_get_config_data_for_srvc_type_or_vertical
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getSrvcReqProfitAndLossData(query) {
        return new Promise((resolve, reject) => {
            query['srvc_type_id'] = this.srvcTypeId || query?.srvc_type_id;
            query['srvc_req_id'] = this.srvcReqId || query?.entry_id;
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);

            const form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            this.db.tms_get_profit_and_loss_details_fr_srvc_req(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_profit_and_loss_details_fr_srvc_req
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    async getSrvcReqsFrAutoAssignLambda(query) {
        var { filters_ } = pagination_filters_utils.decodeQueryParams(query);
        // console.log('query',JSON.stringify(query),'filters_',filters_)
        query['is_customer_access'] = '1';
        const autoAssignLambdaCnfRes = (
            await this.db.tms_get_srvc_reqs_fr_auto_assign_lambda(
                JSON.stringify(query),
                filters_
            )
        )[0].tms_get_srvc_reqs_fr_auto_assign_lambda;
        return autoAssignLambdaCnfRes.data;
    }

    exportProjectsByEmail(query) {
        return new Promise((resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['vertical_id'] = this.vertical_id;
            requester['verticals_list'] = [this.vertical_id];
            requester['srvc_type_id'] = this.srvcTypeId || 0;
            requester['is_customer_access'] = 0;
            const jobData = { requester, filters_ };
            allQueues.WIFY_PROJECTS_EXPORT_BY_EMAIL.addJob(jobData);
            dumpExportReqCounter.inc({
                module: moduleKeys.projectsRequests,
            });
            dumpExportsCounter.inc({
                status: dumpExportStatus.requested,
            });
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    processProjectReqExportByEmail(jobData) {
        return new Promise((resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;
                const dbObj = this.dbReplica || this.db;
                if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }
                dbObj
                    .tms_get_projects_dumps_fr_usr(requesterInfo, filters_, {
                        stream: true,
                    })
                    .then(
                        (stream) => {
                            // we need to start streaming the incoming data
                            // and save to temp folder
                            // once saved trigger email
                            let org_id = jobData?.requester?.org_id;
                            const d = new Date(); // today now
                            let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                            let savePath = path.join(
                                '',
                                'temp_files',
                                'projects_dump',
                                '' + org_id,
                                today
                            );
                            fs.mkdir(savePath, { recursive: true }, (err) => {
                                if (err) {
                                    if (err.code != 'EEXIST') {
                                        return console.log(
                                            'Error in temp folder creation',
                                            err
                                        );
                                    }
                                }

                                let fileName = `Projects dump ${today}_${d.getTime()}.csv`;
                                let filePath = path.join(savePath, fileName);
                                stream.on('end', () => {
                                    // do something with the created file
                                    console.log('Streaming ended -----');

                                    //Send email by QUEUE
                                    let to = jobData.requester?.email_id;
                                    let subject = jobData.requester?.subject;
                                    let message =
                                        '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                                    let attachments = [
                                        { path: filePath, filename: fileName },
                                    ];

                                    //optinal param for save eamil_log
                                    let usr_id = jobData?.requester?.usr_id;
                                    let ip_address =
                                        jobData?.requester?.ip_addr;
                                    let user_agent =
                                        jobData?.requester?.user_agent;

                                    const emailJobData = {
                                        to,
                                        subject,
                                        message,
                                        attachments,
                                        org_id,
                                        usr_id,
                                        ip_address,
                                        user_agent,
                                    };
                                    allQueues.WIFY_SEND_EMAIL.addJob(
                                        emailJobData
                                    );
                                    dumpExportSuccessCounter.inc({
                                        module: moduleKeys.projectsRequests,
                                    });
                                    dumpExportsCounter.inc({
                                        status: dumpExportStatus.success,
                                    });
                                    resolve(
                                        new sampleOperationResp(
                                            true,
                                            'Added to email queue',
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                });

                                console.log('Streaming started');
                                stream
                                    .pipe(JSONStream.stringify())
                                    .pipe(
                                        jsonToCsv({
                                            path: '*.tms_get_projects_dumps_fr_usr',
                                        })
                                    )
                                    .pipe(fs.createWriteStream(filePath));
                            });
                        },
                        (err) => {
                            dumpExportFailureCounter.inc({
                                module: moduleKeys.projectsRequests,
                            });
                            dumpExportsCounter.inc({
                                status: dumpExportStatus.failure,
                            });
                            console.log('EXPORT_DUMP_ERR ......');
                            this.fatalDbError(resolve, err);
                        }
                    );
            } catch (error) {
                dumpExportFailureCounter.inc({
                    module: moduleKeys.projectsRequests,
                });
                dumpExportsCounter.inc({
                    status: dumpExportStatus.failure,
                });
                this.fatalDbError(resolve, error);
            }
        });
    }

    updateAuthoritiesOfSrvcReqs(srvc_req_id) {
        return new Promise(async (resolve, reject) => {
            try {
                //Get service request form_data
                let srvcReqResp = await this.getSingleEntry({}, srvc_req_id);
                if (srvcReqResp.isSuccess()) {
                    let form_data = JSON.parse(srvcReqResp.resp)?.form_data
                        ?.form_data;
                    form_data['is_refresh_authorities_frm_customer_access'] =
                        true;
                    let workflow = getServiceWorkflowModel(this);
                    workflow.autoAuthorityAssignedBasedOnLocation(
                        form_data,
                        srvc_req_id
                    );
                    resolve();
                } else {
                    console.log(
                        'UpdateAuthoritiesOfSrvcReqs getSingleEntry failed',
                        srvc_type_id,
                        srvc_req_id
                    );
                }
            } catch (error) {
                console.log('UpdateAuthoritiesOfSrvcReqs error', error);
            }
        });
    }
    getLocationGrpName(srvc_req_id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_hlpr_get_loc_grps_name_by_srvc_req_id(srvc_req_id).then(
                (res) => {
                    var respData = new db_resp(
                        res[0].tms_hlpr_get_loc_grps_name_by_srvc_req_id
                    );
                    if (!respData.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(respData.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getRoleListByRoleIds(role_ids, orgId = undefined) {
        return new Promise((resolve, reject) => {
            let query = {};
            //added new parameter
            query['org_id'] = orgId || users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['role_ids'] = role_ids;
            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_hlpr_get_role_list_by_role_ids(form_data).then(
                (res) => {
                    var respData = new db_resp(
                        res[0].tms_hlpr_get_role_list_by_role_ids
                    );
                    if (!respData.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(respData.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    callLambdaFnForExecutingDynamicFormLogic(is_customer_access, query) {
        /*
            1. Get service type config data
            2. Get lambda urn
            3. Get service request data
            4. call lambda urn with everything
            5. give back the lambda result
            and ofcourse all that the dyanmic lambda needs meta,allValues,changedValues
        */
        return new Promise(async (resolve, reject) => {
            let config_data = await this.getConfigDataFrSrvcType({});
            // console.log('config_data',config_data);
            let request_data = {};
            if (this.srvcReqId > 0) {
                let srvcReqResp = await this.getSingleEntry({}, this.srvcReqId);
                if (!srvcReqResp.isSuccess()) {
                    resolve(srvcReqResp);
                    return;
                }
                request_data = JSON.parse(srvcReqResp.resp)?.form_data
                    ?.form_data;
            }

            // //Get lambda Arn Url
            let lambdaARN =
                config_data?.srvc_cust_fields_dynamic_form_lambda_arn;
            console.log(
                'callLambdaFnForExecutingDynamicFormLogic lambdaARN',
                lambdaARN
            );

            const isDynamicSPFields = query?.isDynamicSPFields;
            if (isDynamicSPFields) {
                lambdaARN = query?.arn;
            }

            if (lambdaARN == undefined) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Lambda ARN not configured',
                        HttpStatus.StatusCodes.CONFLICT
                    )
                );
            }
            let payload = {
                ...query,
                is_editmode: this.srvcReqId > 0,
                request_data,
            };
            // // console.log('callLambdaFnForExecutingStatusUpdateDynamicFormLogic payload',JSON.stringify(payload))
            // //Call lambda function here
            const params = {
                FunctionName: lambdaARN,
                InvocationType: 'RequestResponse',
                LogType: 'Tail',
                Payload: JSON.stringify(payload),
            };

            try {
                let respData = await callLambdaFn(params);
                let lambdaRespData = JSON.parse(respData.Payload);

                if (lambdaRespData && lambdaRespData.status) {
                    resolve(
                        new sampleOperationResp(
                            lambdaRespData.status,
                            JSON.stringify(lambdaRespData),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            false,
                            lambdaRespData.message,
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                }
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Could not process, please contact admin.',
                        HttpStatus.StatusCodes.CONFLICT
                    )
                );
            }
            resolve(
                new sampleOperationResp(
                    false,
                    'Unknown error',
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                )
            );
        });
    }

    callLambdaFnForProfitAndLossData(
        isCustomerAccess,
        query,
        configData,
        srvcConfigData,
        requestData
    ) {
        return new Promise(async (resolve, reject) => {
            // Choose which config to use for mapping
            const revenueConfig = this.geRevenueColumnConfig(
                this.srvcTypeId,
                configData
            );
            const skuArray =
                revenueConfig.map(
                    (
                        item ///opih7yt654
                    ) =>
                        this.getSkuKey(
                            configData,
                            srvcConfigData,
                            requestData,
                            item.sku
                        )
                ) || [];

            //Get lambda Arn Url
            let lambdaARN = configData?.lambda_arn_profit_loss;

            if (lambdaARN == undefined) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Lambda ARN not configured',
                        HttpStatus.StatusCodes.BAD_REQUEST
                    )
                );
            }
            let payload = {
                ...query,
                org_nick_name: query?.org_nick_name,
                sku_key: skuArray,
                requestData,
                configData,
            };

            //Call lambda function here
            const params = {
                FunctionName: lambdaARN,
                InvocationType: 'RequestResponse',
                LogType: 'Tail',
                Payload: JSON.stringify(payload),
            };

            try {
                let respData = await callLambdaFn(params);
                let lambdaRespData = JSON.parse(respData.Payload);

                if (lambdaRespData && lambdaRespData.status) {
                    resolve(
                        new sampleOperationResp(
                            lambdaRespData.status,
                            JSON.stringify(lambdaRespData),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            false,
                            lambdaRespData.message,
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                }
            } catch (error) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Could not process, please contact admin.',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
        });
    }

    async callLambdaFnForStatusUpdateTrigger(query) {
        return new Promise(async (resolve, reject) => {
            const srvc_req_form_data = query.pre_srvc_req_form_data.form_data;
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            const srvcReqDetails =
                await this.db?.tms_api_brand_get_order_details(
                    JSON.stringify(srvc_req_form_data)
                );
            const srvcReqDetailsData =
                srvcReqDetails[0].tms_api_brand_get_order_details;
            if (srvcReqDetailsData?.status) {
                const lambdaARN = query.service_status_movement_lambda_arn;
                const payload = srvcReqDetailsData.data[0];
                const params = {
                    FunctionName: lambdaARN,
                    InvocationType: 'RequestResponse',
                    LogType: 'Tail',
                    Payload: JSON.stringify({ payload }),
                };
                try {
                    let respData = await callLambdaFn(params);
                    let lambdaRespData = JSON.parse(respData.Payload);
                    if (lambdaRespData && lambdaRespData.status) {
                        resolve(
                            new sampleOperationResp(
                                true,
                                lambdaRespData.message,
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    } else {
                        resolve(
                            new sampleOperationResp(
                                false,
                                lambdaRespData.message,
                                HttpStatus.StatusCodes.BAD_REQUEST
                            )
                        );
                    }
                } catch (error) {
                    console.log(
                        'callLambdaFnForStatusUpdateTrigger :: error :: ',
                        error,
                        query,
                        srvc_req_form_data
                    );
                    this.createTimelineforSrvcReq(
                        query,
                        srvc_req_form_data.display_titles,
                        srvc_req_form_data.entry_id,
                        'UPDATE',
                        error?.message ||
                            'Unable to call RIL lambda successfully.'
                    );
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Cannot send request, please contact admin.',
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                }
            } else {
                console.log('Cannot fetch service status transition details');
            }

            resolve();
        });
    }

    async UpdateLocGrpIdsInSrvcReqs(entry_id, query = {}) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['srvc_type_id'] = this.srvcTypeId;
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db
                .tms_hlpr_update_matched_loc_grp_ids_of_srvc_req(entry_id)
                .then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_hlpr_update_matched_loc_grp_ids_of_srvc_req
                        );
                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            if (query?.refreshLocGroup) {
                                let workflow = getServiceWorkflowModel(this);
                                workflow.processUpdateRatedByOnLocationGroupRefresh(
                                    entry_id,
                                    query
                                );
                            }
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    async getSrvcReqDataById(srvcReqId) {
        try {
            const srvcReqResp = (
                await this.db.tms_get_srvc_req_data_by_req_id(srvcReqId)
            )?.[0].tms_get_srvc_req_data_by_req_id;

            if (srvcReqResp.status) {
                return srvcReqResp.data;
            } else {
                console.log('getSrvcReqDataById failed');
                return 'Failed';
            }
        } catch (error) {
            console.log('getSrvcReqDataById failed', error);
            return 'Failed';
        }
    }

    async sendForBillingNotification(
        query,
        entry_id,
        config_data,
        isSrvcPrvdrTab = false
    ) {
        /* 
            1. Get authorities list from config_data
            3. If authorities is configured from config then
            4. Make a loop on who_will_get_notification_for_billing
            5. Check authorities is exists in srvc_req_form_data is Yes then
            6. Get authority details by authority_id
            7. Get requester details and added CC
            8. send a email for authorities and requester 
        */
        let who_will_get_notification_for_billing =
            config_data?.srvc_type_who_will_get_notified_for_billing;
        if (
            who_will_get_notification_for_billing &&
            who_will_get_notification_for_billing.length > 0
        ) {
            let {
                authorities_list,
                srvc_req_data,
                title,
                org_id,
                billing_final_amount,
            } =
                await this.getAuthoritiesListAndSrvcReqDataFrBillingNotification(
                    entry_id,
                    who_will_get_notification_for_billing
                );

            if (authorities_list && authorities_list.length > 0) {
                let authoritiesEmailId = [];
                authorities_list.forEach((singleAuthority) => {
                    authoritiesEmailId.push(singleAuthority.user_email);
                });

                // To email_ids (Authorities -receiver)
                let to_email_ids = authoritiesEmailId.join();
                this.sendNotificationToReceiverFrSrvcReqSendBilling(
                    query,
                    entry_id,
                    to_email_ids,
                    srvc_req_data,
                    title,
                    org_id,
                    isSrvcPrvdrTab,
                    billing_final_amount
                );
            } else {
                //For timeline entry
                let srvc_type_id = this.srvcTypeId || query.srvc_type_id;
                let timelineMsg = isSrvcPrvdrTab
                    ? 'SP, Notification for billing not sent, no receiver configured'
                    : 'Notification for billing not sent, no receiver configured';
                this.createTimelineforSrvcReq(
                    query,
                    srvc_type_id,
                    entry_id,
                    'UPDATE',
                    timelineMsg
                );
            }
        }
    }

    async getAuthoritiesListAndSrvcReqDataFrBillingNotification(
        entry_id,
        who_will_get_notification_for_billing
    ) {
        let authorities_list = [];
        let returnData = {};
        try {
            const srvcReqResp = await this.getSingleEntry({}, entry_id);
            if (srvcReqResp.isSuccess()) {
                const srvcReqData = JSON.parse(srvcReqResp.resp)?.form_data;
                const srvcReqFormData = srvcReqData?.form_data;

                for (
                    let i = 0;
                    i <= who_will_get_notification_for_billing.length;
                    i++
                ) {
                    let configNotifiedBillingAuthoritiesId =
                        who_will_get_notification_for_billing[i];
                    if (configNotifiedBillingAuthoritiesId) {
                        let authority_key =
                            'authority_' + configNotifiedBillingAuthoritiesId;
                        let authority_id = srvcReqFormData[authority_key];
                        if (authority_id && authority_id != '') {
                            let authorities_details =
                                await this.getUserDetailsById(authority_id);
                            if (authorities_details) {
                                authorities_list.push(authorities_details);
                            }
                        }
                    }
                }
                returnData['authorities_list'] = authorities_list;
                returnData['srvc_req_data'] = srvcReqFormData;
                returnData['title'] = srvcReqData?.title;
                returnData['org_id'] = srvcReqData?.org_id;
                returnData['billing_final_amount'] =
                    srvcReqData?.billing_final_amount;
                return returnData;
            } else {
                console.log(
                    'getAuthoritiesListAndSrvcReqDataFrBillingNotification getSingleEntry failed'
                );
                return returnData;
            }
        } catch (error) {
            console.log(
                'getAuthoritiesListAndSrvcReqDataFrBillingNotification failed',
                error
            );
            return returnData;
        }
    }

    async sendNotificationToReceiverFrSrvcReqSendBilling(
        query,
        entry_id,
        to_email_ids,
        srvc_req_data,
        title,
        org_id,
        isSrvcPrvdrTab,
        billing_final_amount
    ) {
        //Get req_sender_details
        let reqSenderDetails = await this.getUserDetailsById(query.usr_id);
        //Get request org_details
        let requestOrgDetails = await this.getOrgDetails(org_id);

        // Get user org_details
        let userOrgDetails = await this.getOrgDetails(
            reqSenderDetails?.form_data?.org_id
        );

        let spKeyPrefix = isSrvcPrvdrTab ? 'sp_' : '';

        // Create target link based on organization type
        const host = process.env.FRONTEND_URL || 'tms.wify.co.in';
        const customerReqTicketLink =
            userOrgDetails?.org_type == 'ORG_TYPE_SRVC_PRVDR'
                ? `https://${host}/customer-requests/${srvc_req_data.srvc_type_id}?query=${encodeURIComponent(
                      title
                  )}&showItemEditor=true&filters={"is_deleted":[false]}`
                : `https://${host}/services/${srvc_req_data.srvc_type_id}?query=${encodeURIComponent(
                      title
                  )}&showItemEditor=true&filters={"is_deleted":[false]}`;

        let emailNotificationDetails = {
            ticket_id: title,
            org_details: requestOrgDetails, // org_details need beacuse we want brans name
            description: srvc_req_data?.request_description,
            discount: srvc_req_data[`${spKeyPrefix}` + 'final_total_discount'],
            final_amount: billing_final_amount,
            req_sender_name: reqSenderDetails?.user_name,
            targetLink: customerReqTicketLink,
        };

        let subject = `TMS New Billing Activity for ${title}`;
        let message = SrvcReqSendForBillingNotificationTemplate(
            emailNotificationDetails
        );

        this.sendEmail(
            query,
            to_email_ids, // To
            reqSenderDetails?.user_email, // CC
            subject,
            message
        );

        //For timeline entry
        let srvc_type_id = this.srvcTypeId || query.srvc_type_id;
        let timelineMsg = isSrvcPrvdrTab
            ? 'SP, Notification for billing sent'
            : 'Notification for billing sent';

        this.createTimelineforSrvcReq(
            query,
            srvc_type_id,
            entry_id,
            'UPDATE',
            timelineMsg
        );
    }

    async ifAnyChangeInDisApprovalStatusSendEmailToApproverAndRequester(
        query,
        entry_id,
        config_data,
        isSrvcPrvdrTab = false
    ) {
        /* 
            2. Check srvc_type_discount_approval_status_changes_notify is exists from config_data then
            3. Get authorities list, srvc_req and requester data 
            4. Make a loop on srvc_type_discount_approval_status_changes_notify
            5. Check authorities is exists in srvc_req_form_data is Yes then
            6. Get authority details by authority_id
            7. Check send_for_discount_approval_u_by is exists or not if exists then
            8. Get requester details
            9. send a email for authorities and requester 
        */

        let configDisApprovalStatusChangesNotify =
            config_data?.srvc_type_discount_approval_status_changes_notify;
        if (
            configDisApprovalStatusChangesNotify &&
            configDisApprovalStatusChangesNotify.length > 0
        ) {
            let { authorities_list, requester_details, srvc_req_data } =
                await this.getAuthoritiesListSrvcReqAndRequesterData(
                    query,
                    entry_id,
                    config_data,
                    isSrvcPrvdrTab,
                    configDisApprovalStatusChangesNotify
                );

            //send email one by one
            if (authorities_list && authorities_list.length > 0) {
                let authoritiesEmailId = [];

                //get requester_email_id
                let requester_email_id = requester_details?.user_email;
                authoritiesEmailId.push(requester_email_id);

                authorities_list.forEach((singleAuthority) => {
                    authoritiesEmailId.push(singleAuthority.user_email);
                });
                //To Email ids (Requester, Authorities(listener) )
                let to_email_ids = authoritiesEmailId.join();

                this.sendNotificationToAuthorityAndRequesterFrApprovalStatusChange(
                    query,
                    entry_id,
                    to_email_ids,
                    srvc_req_data,
                    isSrvcPrvdrTab
                );
            }
        }
    }

    async getAuthoritiesListSrvcReqAndRequesterData(
        query,
        entry_id,
        config_data,
        isSrvcPrvdrTab,
        configDisApprovalStatusChangesNotify
    ) {
        let authorities_list = [];
        try {
            const srvcReqResp = await this.getSingleEntry({}, entry_id);
            if (srvcReqResp.isSuccess()) {
                const srvc_req_data =
                    await this.getDiscountTotalsAndUpdatedByNameFrSrvcReq(
                        query,
                        srvcReqResp,
                        isSrvcPrvdrTab
                    );
                const { srvcReqFormData } = srvc_req_data;
                let spKeyPrefix = '';
                if (isSrvcPrvdrTab == true || isSrvcPrvdrTab == 'true') {
                    spKeyPrefix = 'sp_';
                }
                for (
                    let i = 0;
                    i <= configDisApprovalStatusChangesNotify.length;
                    i++
                ) {
                    let configAuthorityId =
                        configDisApprovalStatusChangesNotify[i];
                    if (configAuthorityId) {
                        let authority_key = 'authority_' + configAuthorityId;
                        let authority_id = srvcReqFormData[authority_key];

                        if (authority_id && authority_id != '') {
                            let authorities_details =
                                await this.getUserDetailsById(authority_id);
                            if (authorities_details) {
                                authorities_list.push(authorities_details);
                            }
                        }
                    }
                }
                let requester_id =
                    srvcReqFormData[
                        `${spKeyPrefix + 'send_for_discount_approval_u_by'}`
                    ];
                let requester_details =
                    await this.getUserDetailsById(requester_id);

                let returnData = {};
                returnData['authorities_list'] = authorities_list;
                returnData['requester_details'] = requester_details;
                returnData['srvc_req_data'] = srvc_req_data;
                return returnData;
            } else {
                console.log(
                    'getAuthoritiesListSrvcReqAndRequesterData getSingleEntry failed'
                );
            }
        } catch (error) {
            console.log(
                'getAuthoritiesListSrvcReqAndRequesterData failed',
                error
            );
        }
    }

    async sendNotificationToAuthorityAndRequesterFrApprovalStatusChange(
        query,
        entry_id,
        to_email_ids,
        srvc_req_data,
        isSrvcPrvdrTab
    ) {
        const {
            title,
            u_by_usr_name,
            final_sub_total,
            discount_amt,
            final_amount,
        } = srvc_req_data;
        const spKeyPrefix = isSrvcPrvdrTab == true ? 'sp_' : '';
        const discount_approval_status =
            query[`${spKeyPrefix + 'discount_approval_status'}`];

        let emailNotificationDetails = {
            u_by_usr_name: u_by_usr_name,
            sub_total: final_sub_total,
            discount_amt: discount_amt,
            final_amount: final_amount,
            approval_req_status: discount_approval_status,
        };
        let subject = `TMS Discount ${discount_approval_status} for ${title}`;
        let message = discountApprovalStatusChangeNotificationTemplate(
            emailNotificationDetails
        );

        //Get Approver details
        let approver_id = query.usr_id;
        let approver_email_id = (await this.getUserDetailsById(approver_id))
            ?.user_email;

        this.sendEmail(
            query,
            to_email_ids, //to
            approver_email_id, //cc
            subject,
            message
        );

        //For timeline
        let srvc_type_id = this.srvcTypeId || query.srvc_type_id;
        query['to'] = to_email_ids;
        query['cc'] = approver_email_id;
        this.createTimelineforSrvcReq(
            query,
            srvc_type_id,
            entry_id,
            'UPDATE',
            'Notification for approval change sent'
        );
    }

    sendEmail(
        query,
        to,
        cc,
        subject,
        message,
        attachments = [],
        function_name = ''
    ) {
        try {
            //optinal param for save eamil_log
            let org_id = query?.org_id;
            let usr_id = query?.usr_id;
            let ip_address = query?.ip_address;
            let user_agent = query?.user_agent;

            const emailJobData = {
                to,
                cc,
                subject,
                message,
                attachments,
                org_id,
                usr_id,
                ip_address,
                user_agent,
            };
            // console.log("emailJobData",emailJobData)
            allQueues.WIFY_SEND_EMAIL.addJob(emailJobData);
            console.log(`Added to email queue for ${function_name}`);
        } catch (error) {
            console.log(`Send email failed for ${function_name}`, error);
            return;
        }
    }

    async doesLoginUserHasDisApprovalAccess(query) {
        let config_data;
        let hasDisApprovalAccess = {};
        hasDisApprovalAccess['hasDisApprovalAccess'] = false;

        let org_id = users_model.getOrgId(this.userContext);
        let usr_id = users_model.getUUID(this.userContext);
        let srvc_req_id = this.srvcReqId || query.srvc_req_id;
        let srvc_type_id = this.srvcTypeId;
        let isSrvcPrvdrTab = query.isSrvcPrvdrTab;

        const isOrgsrvcPrvdr = await this.isOrgSrvcPrvdr(org_id, srvc_req_id);
        if (isOrgsrvcPrvdr) {
            config_data = await this.getConfigDataFrSrvcprvdr(
                org_id,
                srvc_type_id
            );
        } else {
            config_data = await this.getConfigDataFrSrvcType(query);
        }
        const { approverList } = await this.getSrvcReqDataAndApproverList(
            query,
            srvc_req_id,
            config_data,
            isSrvcPrvdrTab
        );

        //checking login user is exists in approverList ?
        let approver_list_details = approverList.find(
            (x) => x.usr_id === usr_id
        );
        if (
            approver_list_details &&
            Object.keys(approver_list_details).length > 0
        ) {
            hasDisApprovalAccess['hasDisApprovalAccess'] = true;
        }
        // console.log("hasDisApprovalAccess",hasDisApprovalAccess)
        return hasDisApprovalAccess;
    }

    async getSrvcReqDataAndApproverList(
        query,
        entry_id,
        config_data,
        isSrvcPrvdrTab = false
    ) {
        try {
            /*               
                1. get service request details by entry_id
                2. check matching rules from config for (authority or static_user) 
                    and if rules is matching then return approver_details list
            */
            let discounting_rule_config =
                config_data?.srvc_type_billing_discounting_rule_config;
            const srvcReqResp = await this.getSingleEntry({}, entry_id);
            if (srvcReqResp.isSuccess()) {
                const srvc_req_data =
                    await this.getDiscountTotalsAndUpdatedByNameFrSrvcReq(
                        query,
                        srvcReqResp,
                        isSrvcPrvdrTab
                    );
                const { srvcReqFormData, discount_per, discount_amt } =
                    srvc_req_data;

                const approverList =
                    await this.checkMatchingRulesFromConfigAndGetApproverList(
                        discounting_rule_config,
                        srvcReqFormData,
                        discount_per,
                        discount_amt
                    );

                let srvcReqDataAndApproverList = {};
                srvcReqDataAndApproverList['approverList'] = approverList;
                srvcReqDataAndApproverList['srvc_req_data'] = srvc_req_data;
                return srvcReqDataAndApproverList;
            } else {
                console.log(
                    'getSrvcReqDataAndApproverList getSingleEntry failed'
                );
            }
        } catch (error) {
            console.log('getSrvcReqDataAndApproverList failed', error);
        }
    }

    async getDiscountTotalsAndUpdatedByNameFrSrvcReq(
        query,
        reqFormData,
        isSrvcPrvdrTab = false
    ) {
        try {
            let returnReqData = {};
            let srvcReqData = JSON.parse(reqFormData.resp)?.form_data;
            let srvcReqFormData = srvcReqData?.form_data;
            let totalsKeyPrefix = '';
            if (isSrvcPrvdrTab == true || isSrvcPrvdrTab == 'true') {
                totalsKeyPrefix = 'sp_';
            }
            let final_sub_total =
                srvcReqFormData[`${totalsKeyPrefix + 'final_sub_total'}`];
            let final_amount =
                srvcReqFormData[`${totalsKeyPrefix + 'final_amount'}`];
            let final_total_dis_per =
                srvcReqFormData[`${totalsKeyPrefix + 'final_total_dis_per'}`];
            let final_total_discount =
                srvcReqFormData[`${totalsKeyPrefix + 'final_total_discount'}`];

            final_sub_total = numOr0(final_sub_total);
            final_sub_total = final_sub_total == 0 ? 1 : final_sub_total; // if final_sub_total is 0 then set by default 1. bcz 0/0 is undefined in javascript
            //Get discount_per and discount_amt

            returnReqData['final_sub_total'] = final_sub_total;
            returnReqData['final_amount'] = final_amount;
            returnReqData['discount_amt'] = final_total_discount;
            returnReqData['discount_per'] = final_total_dis_per;
            returnReqData['title'] = srvcReqData?.title;
            returnReqData['srvcReqFormData'] = srvcReqFormData;

            //get updated by user_name
            let u_by_usr_name = (await this.getUserDetailsById(query.usr_id))
                ?.user_name;
            returnReqData['u_by_usr_name'] = u_by_usr_name;
            return returnReqData;
        } catch (error) {
            console.log(
                'getDiscountTotalsAndUpdatedByNameFrSrvcReq failed',
                error
            );
        }
    }

    async checkMatchingRulesFromConfigAndGetApproverList(
        discounting_rule_config,
        srvcReqFormData,
        discount_per,
        discount_amt
    ) {
        let approverList = [];
        if (
            discounting_rule_config &&
            !Array.isArray(discounting_rule_config)
        ) {
            let discounting_rules = JSON.parse(discounting_rule_config);
            // console.log("discounting_rules",discounting_rules);
            for (let i = 0; i <= discounting_rules.length; i++) {
                let singleDisRule = discounting_rules[i];
                if (singleDisRule) {
                    let discount_type = singleDisRule.discount_type;
                    let perKeyPrefix =
                        discount_type == 'percentage' ? 'per_' : '';
                    let config_lower_value =
                        singleDisRule[`${perKeyPrefix + 'lower_value'}`];
                    let config_higher_value =
                        singleDisRule[`${perKeyPrefix + 'higher_value'}`];
                    let srvcReqAmtOrPerVal =
                        discount_type == 'percentage'
                            ? discount_per
                            : discount_amt;
                    //conditiona
                    /*
                        1. lower value <= and higher value >=
                            ex- if lower value 100 and higher value 200 hai then
                                100 or 100 k upper wale &&
                                200 or 200 k niche wale sabko email jayega.
                        
                        2. lower value <=
                            ex- if lower value 100 hai or higher me kuch nhi hai then
                            100 or 100 k upper wale sabko email jayega.

                        3. higher value >= 
                            ex- if higher value 100 hai or lower kuch nhi hai then
                            100 or 100 k niche wale sabko email jayega.
                    */
                    let isRuleValid = false;
                    if (config_lower_value && config_higher_value) {
                        if (
                            config_lower_value <= srvcReqAmtOrPerVal &&
                            config_higher_value >= srvcReqAmtOrPerVal
                        ) {
                            isRuleValid = true;
                        }
                    } else if (
                        config_lower_value &&
                        config_higher_value == undefined
                    ) {
                        if (config_lower_value <= srvcReqAmtOrPerVal) {
                            isRuleValid = true;
                        }
                    } else if (
                        config_higher_value &&
                        config_lower_value == undefined
                    ) {
                        if (config_higher_value >= srvcReqAmtOrPerVal) {
                            isRuleValid = true;
                        }
                    }

                    let approver_id = `authority_${singleDisRule?.authority}`;
                    if (
                        singleDisRule.approver_type == 'authority_based' &&
                        srvcReqFormData[approver_id] &&
                        srvcReqFormData[approver_id] != '' &&
                        isRuleValid
                    ) {
                        const approver_details = await this.getUserDetailsById(
                            srvcReqFormData[approver_id]
                        );
                        if (approver_details) {
                            approverList.push(approver_details);
                        }
                    } else if (
                        singleDisRule.approver_type == 'static_user' &&
                        isRuleValid
                    ) {
                        let approver_details = await this.getUserDetailsById(
                            singleDisRule?.user?.value
                        );
                        if (approver_details) {
                            approverList.push(approver_details);
                        }
                    }
                }
            }
        }
        // console.log("approverList",approverList)
        return approverList;
    }

    async checkMatchingRulesAndSendEmailToApprover(
        query,
        entry_id,
        config_data,
        isSrvcPrvdrTab = false
    ) {
        // 1. check discounting_rule_config is exists or not
        let discounting_rule_config =
            config_data?.srvc_type_billing_discounting_rule_config;
        if (discounting_rule_config) {
            const approverListAndSrvcReqData =
                await this.getSrvcReqDataAndApproverList(
                    query,
                    entry_id,
                    config_data,
                    isSrvcPrvdrTab
                );
            const { approverList, srvc_req_data } = approverListAndSrvcReqData;
            const {
                title,
                u_by_usr_name,
                final_sub_total,
                discount_amt,
                final_amount,
            } = srvc_req_data;

            // console.log("approverList",approverList)
            if (approverList && approverList.length > 0) {
                //make a loop on approverList and send email one by one approver.
                approverList.forEach((singleApproverDetails) => {
                    this.sendEmailToApproverFrDiscountNotification(
                        query,
                        title,
                        singleApproverDetails,
                        u_by_usr_name,
                        final_sub_total,
                        discount_amt,
                        final_amount
                    );
                });
            } else {
                // If there is no approver selected and no matching discount rule found , then an entry has to be saved in the timeline.
                let srvc_type_id = this.srvcTypeId || query.srvc_type_id;
                let timeline_title =
                    'No approver selected / no matching discount rule found';
                this.createTimelineforSrvcReq(
                    query,
                    srvc_type_id,
                    entry_id,
                    'UPDATE',
                    timeline_title
                );
            }
        }
    }

    sendEmailToApproverFrDiscountNotification(
        query,
        title,
        approver_details,
        updated_by_usr_name,
        final_sub_total,
        discount_amt,
        final_amount
    ) {
        let emailNotificationDetails = {
            approver_usr_name: approver_details?.user_name,
            updated_by_usr_name: updated_by_usr_name,
            sub_total: final_sub_total,
            discount_amt: discount_amt,
            final_amount: final_amount,
        };
        try {
            //Send email by QUEUE
            let to = approver_details?.user_email;
            let subject = `TMS discount approval requested for ${title}`;
            let message = discountApprovalAuthoritiesNotificationTemplate(
                emailNotificationDetails
            );
            let attachments = [];

            //optinal param for save eamil_log
            let org_id = query?.org_id;
            let usr_id = query?.usr_id;
            let ip_address = query?.ip_address;
            let user_agent = query?.user_agent;

            const emailJobData = {
                to,
                subject,
                message,
                attachments,
                org_id,
                usr_id,
                ip_address,
                user_agent,
            };
            // console.log("emailJobData",emailJobData)
            allQueues.WIFY_SEND_EMAIL.addJob(emailJobData);
            console.log(
                'Added to email queue for sendEmailToApproverFrDiscountNotification'
            );
        } catch (error) {
            console.log(
                'Send email failed for sendEmailToApproverFrDiscountNotification',
                error
            );
            return;
        }
    }

    async isOrgSrvcPrvdr(org_id, srvc_req_id) {
        try {
            const isOrgSrvcPrvdr = (
                await this.db.tms_hlpr_is_org_srvc_prvdr_by_srvc_req_and_org_id(
                    org_id,
                    srvc_req_id
                )
            )?.[0].tms_hlpr_is_org_srvc_prvdr_by_srvc_req_and_org_id;

            return isOrgSrvcPrvdr;
        } catch (error) {
            console.log('isOrgSrvcPrvdr failed', error);
            return 'Failed';
        }
    }

    async getConfigDataFrSrvcType(query) {
        try {
            const operationResp = await this.getSrvcTypeConfigData(query);
            if (operationResp.isSuccess()) {
                return JSON.parse(operationResp.resp)?.config_data;
            } else {
                throw 'Srvc type config data not available';
            }
        } catch (error) {
            console.log('getConfigDataFrSrvcType failed', error);
            return 'Failed';
        }
    }
    async getConfigDataFrSrvcTypeOrVerticalType(query) {
        try {
            const operationResp =
                await this.getSrvcTypeOrVerticalTypeConfigData(query);
            if (operationResp.isSuccess()) {
                return JSON.parse(operationResp.resp)?.config_data;
            } else {
                throw 'Srvc type or Vertical type config data not available';
            }
        } catch (error) {
            console.log(
                'getConfigDataFrSrvcTypeOrVerticalType :: failed ::',
                error
            );
            return error.message;
        }
    }

    findTechnicianCost = (technicianMasterData, sku, returnValueType) => {
        const item = technicianMasterData.find((item) => item.Item_SKU === sku);
        return item ? parseFloat(item[returnValueType]) : null;
    };

    findExpectedAndActualPrice = (
        technicianMasterData,
        sku,
        returnValueType
    ) => {
        const item = technicianMasterData.find((item) => item.Item_SKU === sku);
        return item ? parseFloat(item[returnValueType]) : null;
    };

    geRevenueColumnConfig(
        srvc_type_id,
        configData,
        isSrvcTypeConfigured = false
    ) {
        const srvcTypeRevenueConfig = JSON.parse(
            configData?.srvc_type_revenue_column_meta || '[]'
        );

        const verticalRevenueConfig = JSON.parse(
            configData?.revenue_column_meta || '[]'
        );

        // Check if srvcTypeRevenueConfig has relevant keys with values and matches srvc_type_id
        const validConfig = srvcTypeRevenueConfig.filter(
            (config) =>
                config.srvc_type == srvc_type_id &&
                (config.item_name || config.qty || config.sku)
        );
        if (isSrvcTypeConfigured) {
            return validConfig.length > 0 ? true : false;
        } else {
            return validConfig.length > 0 ? validConfig : verticalRevenueConfig;
        }
    }

    getFinalProfitAndLossResp(
        parsedLambdaResp,
        configData,
        srvcConfigData,
        requestData,
        parsedSrvcReqProditAndLossResp,
        isLambdaDataRequired = false
    ) {
        const lambdasData = JSON.parse(parsedLambdaResp.body || {});
        const technicianDataFrCostBreakdown =
            parsedSrvcReqProditAndLossResp?.technician_data_fr_cost_breakdown;
        let finalData = {};

        const revenueConfig = this.geRevenueColumnConfig(
            this.srvcTypeId,
            configData
        );

        const userResourceTypeConfig = configData?.userFieldFrResourceType;
        const additionalRevenueField = configData?.sp_additional_revenue_field;
        const revenueMasterData = lambdasData?.revenue_master_data || [];
        const technicianMasterData = lambdasData?.technician_master_data || [];
        const additionalCost =
            parseInt(requestData?.sp_final_sub_total || '0') +
            (lambdasData?.spAdditionalAmount || 0);
        const deduction = parseInt(requestData?.sp_final_deduction || '0');
        const totalDiscount = parseInt(
            requestData?.sp_final_total_discount || '0'
        );
        let needToUpdateInDb = false;
        let totalCost = 0;
        let totalRevenue = 0;
        let skuChanged = false;
        let additionalRevenueCost = 0;

        finalData['revenue_master_data'] = {};
        finalData['technician_master_data'] = {};

        if (technicianDataFrCostBreakdown?.length > 0) {
            finalData['technician_data_fr_cost_breakdown'] =
                technicianDataFrCostBreakdown;
        }

        if (parsedSrvcReqProditAndLossResp?.user_custom_fields) {
            finalData['user_custom_fields'] =
                parsedSrvcReqProditAndLossResp?.user_custom_fields;
        }
        for (const singleObj of revenueConfig) {
            const skuKey = this.getSkuKey(
                configData,
                srvcConfigData,
                requestData,
                singleObj?.sku
            );
            let subTotal = 0;
            const revenueQuantity = requestData[`${singleObj?.qty}`];
            let technicianCost = 0;
            let actualRevenue = 0;
            let expectedRevenue = 0;
            skuChanged =
                skuKey !=
                parsedSrvcReqProditAndLossResp?.profit_and_loss_data?.sku;

            if (isLambdaDataRequired || skuChanged) {
                technicianCost = this.findTechnicianCost(
                    technicianMasterData,
                    skuKey,
                    'Technician Cost'
                );
            } else {
                technicianCost =
                    parsedSrvcReqProditAndLossResp?.profit_and_loss_data
                        ?.technician_master_data?.[`${skuKey}_technicianCost`];
            }

            if (isLambdaDataRequired || skuChanged) {
                actualRevenue = this.findExpectedAndActualPrice(
                    revenueMasterData,
                    skuKey,
                    'Actual Price'
                );
            } else {
                actualRevenue =
                    parsedSrvcReqProditAndLossResp?.profit_and_loss_data
                        ?.revenue_master_data?.[
                        `${skuKey}_actual_revenue_fr_sheet`
                    ];
            }

            if (isLambdaDataRequired || skuChanged) {
                expectedRevenue = this.findExpectedAndActualPrice(
                    revenueMasterData,
                    skuKey,
                    'Expected Price'
                );
            } else {
                expectedRevenue =
                    parsedSrvcReqProditAndLossResp?.profit_and_loss_data
                        ?.revenue_master_data?.[
                        `${skuKey}_expected_revenue_fr_sheet`
                    ];
            }

            finalData['sku'] = skuKey;

            if (revenueMasterData.length > 0) {
                finalData['revenue_master_data'][`${skuKey}_expectedRevenue`] =
                    expectedRevenue * (revenueQuantity || 1);

                finalData['revenue_master_data'][`${skuKey}_actualRevenue`] =
                    actualRevenue * (revenueQuantity || 1);
                finalData['revenue_master_data'][
                    `${skuKey}_expected_revenue_fr_sheet`
                ] = expectedRevenue;

                finalData['revenue_master_data'][
                    `${skuKey}_actual_revenue_fr_sheet`
                ] = actualRevenue;
            }

            finalData['revenue_master_data'][`${singleObj?.qty}`] =
                revenueQuantity;

            finalData['revenue_master_data'][`${skuKey}`] =
                requestData[`${skuKey}`];

            finalData['revenue_master_data'][`${singleObj?.item_name}`] =
                requestData[`${singleObj?.item_name}`];
            totalRevenue =
                totalRevenue + actualRevenue * (revenueQuantity || 1);

            let mandayCount = 0;

            if (technicianCost) {
                for (const singleTechnicianData of technicianDataFrCostBreakdown) {
                    const mandays = singleTechnicianData?.mandays;
                    if (mandays) {
                        mandayCount = mandayCount + mandays;
                    }
                    const actualAndExpectedCost =
                        technicianCost * (mandays || 1);
                    finalData['technician_master_data'][
                        `${singleTechnicianData?.user_id}_expectedCost`
                    ] = actualAndExpectedCost;

                    finalData['technician_master_data'][
                        `${singleTechnicianData?.user_id}_actualCost`
                    ] = actualAndExpectedCost;
                    subTotal = subTotal + actualAndExpectedCost;
                }
                if (mandayCount > 0) {
                    finalData['technician_master_data'][
                        `${skuKey}_technicianCost`
                    ] = technicianCost;
                }
            }
            totalCost = totalCost + subTotal;
        }

        // get additional revenue cost from request data
        if (additionalRevenueField) {
            const latestValue = requestData[additionalRevenueField] || 0;

            // Truncate to 2 decimals
            if (latestValue < 0) {
                additionalRevenueCost = Math.ceil(latestValue * 100) / 100; // negatives
            } else {
                additionalRevenueCost = Math.floor(latestValue * 100) / 100; // positives
            }

            const previousValue =
                parsedSrvcReqProditAndLossResp?.profit_and_loss_data
                    ?.additional_revenue ?? 0;
            // check if revenue cost changed
            needToUpdateInDb = additionalRevenueCost !== previousValue;
        }

        finalData['additional_revenue'] = additionalRevenueCost;
        totalRevenue = totalRevenue + additionalRevenueCost;
        totalCost = totalCost + additionalCost - deduction - totalDiscount;
        finalData['total_cost'] = totalCost;
        finalData['total_revenue'] = totalRevenue;
        finalData['net_gm'] = totalRevenue - totalCost;
        finalData['net_gm'] = parseFloat((totalRevenue - totalCost).toFixed(2));
        finalData['gm_per'] = (
            ((totalRevenue - totalCost) / totalRevenue) *
            100
        ).toFixed(2);
        finalData['additional_cost'] = additionalCost;
        finalData['deduction'] = deduction;
        finalData['discount'] = totalDiscount;
        return { ...finalData, skuChanged, needToUpdateInDb };
    }

    createTimelineforSrvcReq(
        query,
        srvc_type_id,
        srvc_req_id,
        update_type,
        timeline_title
    ) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            let form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db
                .tms_add_to_srvc_req_timeline(
                    srvc_type_id,
                    srvc_req_id,
                    update_type,
                    timeline_title,
                    form_data
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_add_to_srvc_req_timeline
                        );
                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    async triggerNotificationToAuthorityFrFieldUpdate(
        singleNotification,
        srvc_req_form_data,
        singleQueryField,
        query,
        brand_config_data,
        sp_config_data
    ) {
        let notification_authorities =
            singleNotification?.notification_authorities;
        if (
            notification_authorities &&
            Array.isArray(notification_authorities)
        ) {
            let emailSuccessOrFailureDataFrTimeline = {};

            for (const singleAuthorityNotification of notification_authorities) {
                let srvc_req_authority_key =
                    'authority_' + singleAuthorityNotification;
                let authorities_id =
                    srvc_req_form_data?.form_data?.[srvc_req_authority_key];

                if (authorities_id && authorities_id !== '') {
                    let allFieldsMeta =
                        await this.getSpecificFieldsNameBySpecificId(
                            singleQueryField,
                            brand_config_data,
                            sp_config_data
                        );
                    let valueDataFromMeta = commonUtils.getValueDataFrmFormMeta(
                        allFieldsMeta,
                        query
                    );

                    let authorities_details =
                        await this.getUserDetailsById(authorities_id);
                    let updated_by_usr_name = (
                        await this.getUserDetailsById(query.usr_id)
                    )?.user_name;

                    //get org_details to notify
                    const org_details = await this.getOrgDetails(
                        authorities_details?.form_data?.org_id
                    );
                    const host = process.env.FRONTEND_URL || 'tms.wify.co.in';

                    let target_link_acc_to_org_type =
                        org_details?.org_type == 'ORG_TYPE_SRVC_PRVDR'
                            ? `https://${host}/customer-requests?query=${srvc_req_form_data.title}`
                            : `https://${host}/services/${srvc_req_form_data.srvc_type_id}?query=${srvc_req_form_data.title}`;

                    let emailNotificationDetails = {
                        specific_field_val:
                            valueDataFromMeta?.[singleQueryField] || '',
                        specific_field_name:
                            await this.getSpecificFieldsNameById(
                                allFieldsMeta,
                                singleQueryField
                            ),
                        authorities_usr_name: authorities_details?.user_name,
                        updated_by_usr_name: updated_by_usr_name,
                        targetLink: target_link_acc_to_org_type,
                    };

                    let emailQuery = {};
                    emailQuery[srvc_req_authority_key] =
                        authorities_details?.user_name;

                    try {
                        let to = authorities_details?.user_email;
                        let subject = `TMS ${srvc_req_form_data.title} ${emailNotificationDetails?.specific_field_name} (Field updated)`;
                        let message =
                            specificFieldsAuthoritiesNotificationEmailTemplate(
                                emailNotificationDetails
                            );
                        let attachments = [];
                        let org_id = query?.org_id;
                        let usr_id = query?.usr_id;
                        let ip_address = query?.ip_address;
                        let user_agent = query?.user_agent;

                        const emailJobData = {
                            to,
                            subject,
                            message,
                            attachments,
                            org_id,
                            usr_id,
                            ip_address,
                            user_agent,
                        };

                        allQueues.WIFY_SEND_EMAIL.addJob(emailJobData);
                        emailSuccessOrFailureDataFrTimeline['success'] =
                            emailSuccessOrFailureDataFrTimeline['success']
                                ? {
                                      ...emailSuccessOrFailureDataFrTimeline[
                                          'success'
                                      ],
                                      ...emailQuery,
                                  }
                                : emailQuery;

                        console.log(
                            'Added to email queue for triggerNotificationToAuthorityFrFieldUpdate'
                        );
                    } catch (error) {
                        emailSuccessOrFailureDataFrTimeline['failure'] =
                            emailSuccessOrFailureDataFrTimeline['failure']
                                ? {
                                      ...emailSuccessOrFailureDataFrTimeline[
                                          'failure'
                                      ],
                                      ...emailQuery,
                                  }
                                : emailQuery;

                        console.log(
                            'Send email failed for triggerNotificationToAuthorityFrFieldUpdate',
                            error
                        );
                        return;
                    }
                }
            }

            // console.log("emailSuccessOrFailureDataFrTimeline", emailSuccessOrFailureDataFrTimeline);

            for (const singleEmailSuccessOrFailureDataFrTimeline of Object.keys(
                emailSuccessOrFailureDataFrTimeline
            )) {
                if (
                    emailSuccessOrFailureDataFrTimeline[
                        singleEmailSuccessOrFailureDataFrTimeline
                    ]
                ) {
                    let srvcTypeId = this.srvcTypeId;
                    let srvcReqId = srvc_req_form_data.id;
                    let timelineMsg =
                        singleEmailSuccessOrFailureDataFrTimeline == 'success'
                            ? 'Notified authority via email'
                            : 'Failed to notify authority via email';
                    let formData =
                        emailSuccessOrFailureDataFrTimeline[
                            singleEmailSuccessOrFailureDataFrTimeline
                        ];

                    this.createTimelineforSrvcReq(
                        formData,
                        srvcTypeId,
                        srvcReqId,
                        'UPDATE',
                        timelineMsg
                    );
                }
            }
        }
    }

    async getSpecificFieldsNameById(allFieldsMeta, singleQueryField) {
        let translatedSpecificFieldsName = '';
        if (allFieldsMeta.length > 0) {
            let translatedFieldsName = allFieldsMeta.filter(
                (singleField) => singleField.key == singleQueryField
            )?.[0]?.label;
            translatedSpecificFieldsName = translatedFieldsName;
        }
        return translatedSpecificFieldsName;
    }

    async getSpecificFieldsNameBySpecificId(
        specific_field,
        brand_config_data,
        sp_config_data
    ) {
        let allTranslatedSpecificFields = [];
        if (brand_config_data && brand_config_data != '') {
            let brandTranslatedSpecificFields = JSON.parse(
                brand_config_data?.srvc_cust_fields_json
            )?.translatedFields;
            allTranslatedSpecificFields.push(...brandTranslatedSpecificFields);
        }
        if (sp_config_data && sp_config_data != '') {
            let spTranslatedSpecificFields = JSON.parse(
                sp_config_data?.sp_cust_fields_json
            )?.translatedFields;
            allTranslatedSpecificFields.push(...spTranslatedSpecificFields);
        }
        return allTranslatedSpecificFields;
    }

    async getConfigDataFrSrvcprvdr(org_id, srvc_type_id) {
        try {
            const sp_config_data = (
                await this.db.get_hlpr_sp_config_data(org_id, srvc_type_id)
            )?.[0].get_hlpr_sp_config_data;

            return sp_config_data;
        } catch (error) {
            console.log('getConfigDataFrSrvcprvdr failed', error);
            return 'Failed';
        }
    }

    async getUserDetailsById(user_id) {
        try {
            const userResp = (await this.db.tms_get_user_details(user_id))?.[0]
                .tms_get_user_details;

            if (userResp.status) {
                return userResp.data;
            } else {
                console.log('getUserDetailsById failed');
                return 'Failed';
            }
        } catch (error) {
            console.log('userResp failed', error);
            return 'Failed';
        }
    }

    async getOrgDetails(org_id) {
        try {
            const orgDetailsResp = (
                await this.db.tms_get_org_details(org_id)
            )?.[0].tms_get_org_details;

            if (orgDetailsResp.status) {
                return orgDetailsResp.data;
            } else {
                console.log('getOrgDetails failed');
                return 'Failed';
            }
        } catch (error) {
            console.log('orgDetailsResp failed', error);
            return 'Failed';
        }
    }

    getSrvcReqDeploymentDailyUpdateData(query, srvc_req_id, tab_name) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_req_id'] = srvc_req_id;
            query['tab_name'] = tab_name;
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var form_data = JSON.stringify(query);
            this.db.tms_get_srvc_req_deployment_daily_update(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_srvc_req_deployment_daily_update
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getSbtskDetailsFrCalendar(query) {
        return new Promise((resolve, reject) => {
            // query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var form_data = JSON.stringify(query);
            this.db.tms_get_sbtsk_details_for_calendar(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_sbtsk_details_for_calendar
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getSrvcReqCalendarData(query) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_req_id'] = this.srvcReqId;

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var form_data = JSON.stringify(query);
            this.db.tms_get_srvc_req_calendar_data(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_srvc_req_calendar_data
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getAppCallLog(query) {
        return new Promise((resolve, reject) => {
            this.getTimelineDetailsById(query.txn_log_id).then((resResult) => {
                if (!resResult.isSuccess()) {
                    resolve(resResult);
                    return;
                } else if (resResult.isSuccess()) {
                    let respData = JSON.parse(resResult.resp);
                    //call app_call.js function
                    app_call.appCallStatus(respData).then((resp) => {
                        resolve(resp);
                        return;
                    });
                }
            });
        });
    }

    initiateConsumerCall(query) {
        return new Promise((resolve, reject) => {
            this.getSingleEntry(query, query.srvc_id).then((operationResp) => {
                if (!operationResp.isSuccess()) {
                    resolve(operationResp);
                    return;
                } else if (operationResp.isSuccess()) {
                    this.checkUsrHasAccessToCallConsumer(query).then(
                        (resResult) => {
                            if (!resResult.isSuccess()) {
                                resolve(resResult);
                                return;
                            } else if (resResult.isSuccess()) {
                                let respData = JSON.parse(resResult.resp);
                                query['user_name'] = respData.user_name;
                                query['user_mobile_no'] =
                                    respData.user_mobile_no;
                                query['cust_name'] = respData.cust_name;
                                query['cust_mobile_no'] =
                                    respData.cust_mobile_no;
                                query['for_org_id'] = respData.for_org_id;
                                query['srvc_req_id'] = respData.srvc_req_id;
                                query['org_id'] = users_model.getOrgId(
                                    this.userContext
                                );
                                let day_call_count = respData.day_call_count;

                                //Entry should go to Timeline only if the user has access.
                                //Create timeline entry for -> user tried calling consumer.
                                this.createTimelineforAppCall(query, 'CALL');

                                if (day_call_count > Daily_max_calls_allowed) {
                                    resolve(
                                        new sampleOperationResp(
                                            false,
                                            'max_call_exceeded',
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                    return;
                                }
                                //call app_call.js function
                                app_call
                                    .initiateConnectCall(this, query)
                                    .then((resp) => {
                                        resolve(resp);
                                        return;
                                    });
                            }
                        }
                    );
                }
            });
        });
    }

    createTimelineforAppCall(query, update_type) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;

            let srvc_type_id =
                update_type == 'CALL'
                    ? query.srvc_type_id
                    : query.request_type_id;
            let srvc_id =
                update_type == 'CALL' ? query.srvc_id : query.request_id;
            let timeline_title =
                update_type == 'CALL'
                    ? query.user_name +
                      ' tried calling ' +
                      query.cust_name +
                      ' (Consumer)'
                    : 'Call details ' + query.user_name + ' with consumer';
            let update_type_ = update_type;
            let form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db
                .tms_add_to_srvc_req_timeline(
                    srvc_type_id,
                    srvc_id,
                    update_type_,
                    timeline_title,
                    form_data
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_add_to_srvc_req_timeline
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    checkUsrHasAccessToCallConsumer(query) {
        return new Promise((resolve, reject) => {
            let form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_check_usr_has_access_to_call_consumer(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_check_usr_has_access_to_call_consumer
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getTimelineDetailsById(txn_log_id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_timeline_details_by_id(txn_log_id).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_timeline_details_by_id
                    );
                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    syncSrvcReqLocWithCache(new_entry_id = 0) {
        return new Promise((resolve, reject) => {
            this.getSingleEntry({}, new_entry_id).then((operationResp) => {
                // console.log("syncSrvcReqLocWithCache getSingleEntry operationResp->",operationResp);
                if (operationResp.isSuccess()) {
                    let form_data = JSON.parse(operationResp.resp).form_data;
                    //chk address exists or not from sys_geo_coding_cache table
                    let addrs = geo_coding_utils.concatAddress(
                        form_data?.form_data
                    );
                    // console.log("syncSrvcReqLocWithCache addrs ->",addrs);
                    this.checkLocFromCacheByAddress(addrs).then(
                        (operationResp) => {
                            // console.log("syncSrvcReqLocWithCache checkLocFromCacheByAddress operationResp-",operationResp);
                            if (operationResp.success) {
                                //updateLocTosrvcReq
                                this.updateLocTosrvcReq(
                                    operationResp,
                                    new_entry_id
                                );
                                resolve();
                            } else {
                                // start a bull job
                                reject({ form_data: form_data });
                            }
                        }
                    );
                } else {
                    // since cron job will handle such failures
                    // we say resolved
                    resolve();
                }
            });
        });
    }

    syncCalenderDataWithSrvcReqFormData(body, params) {
        return new Promise(async (resolve, reject) => {
            try {
                let dbResp = await subtasks_model.createdOrUpdateDeployment(
                    params,
                    body
                );
                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify('Synced Successfully'),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.log('error', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        JSON.stringify(error),
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    // will block the thread
    // so make sure its called in a background job
    async processLocVerficationOfSubtasks(service_req_id) {
        if (!(service_req_id > 0)) {
            return;
        }
        // get all subtasks with loc verfication
        // and processGeofenceAutomations on them
        try {
            let allSubtasksResp = (
                await this.db.tms_get_sbtsks_fr_srvc_req(service_req_id)
            )[0]['tms_get_sbtsks_fr_srvc_req'];
            // console.log('allSubtasks',allSubtasks);
            let subtasks = allSubtasksResp.data;
            setParamsToSubtaskModel(
                subtasks_model,
                this.getServicesModelData(this, true),
                this.db
            );
            for (let index = 0; index < subtasks.length; index++) {
                const singleSbtskDetails = subtasks[index];
                let sbtsk_db_id = singleSbtskDetails.id;
                let sbtsk_type_id = singleSbtskDetails.sbtsk_type_id;
                let update_type_data =
                    singleSbtskDetails.form_data?.update_type_data;
                if (update_type_data) {
                    let updateTypeIds = Object.keys(update_type_data);
                    for (let i = 0; i < updateTypeIds.length; i++) {
                        const updateTypeId = updateTypeIds[i];
                        let update_data = update_type_data[updateTypeId];
                        if (update_data.user_gps_location_status) {
                            await subtasks_model.updateGPSLocationVerfication(
                                sbtsk_type_id,
                                sbtsk_db_id,
                                updateTypeId,
                                this
                            );
                        }
                    }
                }
            }
            return 'done';
        } catch (error) {
            console.error(error);
            return 'Failed';
        }
    }

    updateLocTosrvcReq(form_data, new_entry_id) {
        let query = {};
        query['geocoding_location_data'] = JSON.parse(form_data.resp);
        console.log('updateLocTosrvcReq form_data', query);
        this.createOrUpdate(query, new_entry_id).then((resResult) => {
            // Whenever a upadte in location happens
            // all subtask updates with loc_verification status
            // need to be reverified
            const jobData = {
                query,
                srvc_req_id: new_entry_id,
                services_model_data: this.getServicesModelData(this),
            };
            allQueues.WIFY_SRVC_REQ_GPS_VERIFICATION_OF_SUBTASKS.addJob(
                jobData
            );
            // performLocationVerificationOfAllSubtasks(this.db,query,new_entry_id,this);
        });
    }

    checkLocFromCacheByAddress(address) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_loc_from_cache_by_addrs(address).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_loc_from_cache_by_addrs
                    );

                    if (dbResp.code == 'not_found') {
                        // console.log('Failed to update',dbResp.code)
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getCustAccessOverviewProto(
        query,
        cust_selected_org_id,
        cust_selected_srvc_type_id
    ) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['cust_selected_org_id'] = cust_selected_org_id;
            query['cust_selected_srvc_type_id'] = cust_selected_srvc_type_id;

            var form_data = JSON.stringify(query);
            //    console.log("form_data",form_data);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_cust_access_overview_proto(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_cust_access_overview_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getDataFrNotification(query, entry_id) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_type_id'] = this.srvcTypeId;
            query['entry_id'] = entry_id;

            var form_data = JSON.stringify(query);

            if (!this.db) {
                reject(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            console.log('form_data for notification', form_data);
            this.db.tms_get_srvc_req_details_fr_notification(form_data).then(
                (res) => {
                    if (!res || !res[0]) {
                        reject(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(
                        res[0].tms_get_srvc_req_details_fr_notification
                    );

                    if (!dbResp.status) {
                        reject(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(dbResp.data);
                    }
                },
                (error) => {
                    reject(error);
                }
            );
        });
    }

    validateMaskedNumberAndPin(query, entry_id) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_type_id'] = this.srvcTypeId;
            query['entry_id'] = entry_id;

            var form_data = JSON.stringify(query);

            if (!this.db) {
                reject(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            console.log('form_data for validateMaskedNumberAndPin', form_data);
            this.db.tms_validate_masked_number_and_pin(form_data).then(
                (res) => {
                    if (!res || !res[0]) {
                        reject(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(
                        res[0].tms_validate_masked_number_and_pin
                    );

                    if (!dbResp.status) {
                        reject(
                            new sampleOperationResp(
                                false,
                                dbResp.code,
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(dbResp.data);
                    }
                },
                (error) => {
                    reject(error);
                }
            );
        });
    }

    getSrvcReqFormDataFrAutomation(query, entry_id) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_type_id'] = this.srvcTypeId;
            query['entry_id'] = entry_id;

            var form_data = JSON.stringify(query);

            if (!this.db) {
                reject(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            // console.log('form_data for automation',form_data);
            this.db.tms_get_srvc_req_form_data_fr_automation(form_data).then(
                (res) => {
                    if (!res || !res[0]) {
                        reject(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(
                        res[0].tms_get_srvc_req_form_data_fr_automation
                    );

                    if (!dbResp.status) {
                        reject(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(dbResp.data);
                    }
                },
                (error) => {
                    reject(error);
                }
            );
        });
    }

    getAuthorityIdByLocationGroup(org_id, pincode, role_id) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            // console.log('form_data for automation',form_data);
            this.db
                .tms_get_authority_user_by_location_group(
                    org_id,
                    pincode,
                    role_id
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            reject(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(
                            res[0].tms_get_authority_user_by_location_group
                        );

                        if (!dbResp.status) {
                            reject(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(dbResp.data);
                        }
                    },
                    (error) => {
                        reject(error);
                    }
                );
        });
    }

    exportServicesByEmail(query, is_customer_access = 0) {
        return new Promise((resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_addr'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['srvc_type_id'] = this.srvcTypeId;
            requester['is_customer_access'] = is_customer_access;

            const service_model_data = this.getServicesModelData(this);
            const jobData = { requester, filters_, service_model_data };
            allQueues.WIFY_SRVC_REQ_EXPORT_BY_EMAIL.addJob(jobData);

            dumpExportReqCounter.inc({
                module: moduleKeys.serviceRequests,
            });
            dumpExportsCounter.inc({
                status: dumpExportStatus.requested,
            });
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    // processSrvcReqExportByEmailTest(jobData){
    //     return new Promise((resolve,reject)=>{
    //         setTimeout(()=>{
    //             resolve()
    //         },25000);
    //     })
    // }

    processSrvcReqExportByEmail(jobData, job_id = 0) {
        return new Promise((resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;
                const dbObj = this.dbDump || this.dbReplica || this.db;
                if (this.dbDump) {
                    console.log('Loading data from Dump');
                } else if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }
                console.log(
                    `${job_id} - Initiate DB tms_get_srvc_req_dumps_fr_usr`
                );
                console.log(
                    'exportServicesByEmail :: form_data',
                    requesterInfo,
                    filters_
                );
                dbObj
                    .tms_get_srvc_req_dumps_fr_usr(requesterInfo, filters_, {
                        stream: true,
                    })
                    .then(
                        (stream) => {
                            console.log(`${job_id} - Initiate stream`);
                            // we need to start streaming the incoming data
                            // and save to temp folder
                            // once saved trigger email
                            let org_id = jobData?.requester?.org_id;
                            const d = new Date(); // today now
                            let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                            let savePath = path.join(
                                '',
                                'temp_files',
                                'request_dump',
                                '' + org_id,
                                today
                            );
                            // console.log('savePath',savePath);

                            fs.mkdir(savePath, { recursive: true }, (err) => {
                                if (err) {
                                    if (err.code != 'EEXIST') {
                                        return console.log(
                                            'Error in temp folder creation',
                                            err
                                        );
                                    }
                                } else {
                                    // console.log('Directory created successfully!');
                                }

                                let fileName = `Service Requests dump ${today}_${d.getTime()}.csv`;
                                let filePath = path.join(savePath, fileName);
                                stream.on('end', () => {
                                    // do something with the created file
                                    console.log(`${job_id} - Streaming ended`);
                                    console.log('Streaming ended -----');

                                    //Send email by QUEUE
                                    let to = jobData.requester?.email_id;
                                    let subject = jobData.requester?.subject;
                                    let message =
                                        '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                                    let attachments = [
                                        { path: filePath, filename: fileName },
                                    ];

                                    //optinal param for save eamil_log
                                    let usr_id = jobData?.requester?.usr_id;
                                    let ip_address =
                                        jobData?.requester?.ip_addr;
                                    let user_agent =
                                        jobData?.requester?.user_agent;

                                    const emailJobData = {
                                        to,
                                        subject,
                                        message,
                                        attachments,
                                        org_id,
                                        usr_id,
                                        ip_address,
                                        user_agent,
                                    };
                                    allQueues.WIFY_SEND_EMAIL.addJob(
                                        emailJobData
                                    );
                                    dumpExportSuccessCounter.inc({
                                        module: moduleKeys.serviceRequests,
                                    });
                                    dumpExportsCounter.inc({
                                        status: dumpExportStatus.success,
                                    });

                                    resolve(
                                        new sampleOperationResp(
                                            true,
                                            'Added to email queue',
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                });

                                console.log(`${job_id} - Streaming started`);
                                console.log('Streaming started');
                                stream
                                    .pipe(JSONStream.stringify())
                                    .pipe(
                                        jsonToCsv({
                                            path: '*.tms_get_srvc_req_dumps_fr_usr',
                                        })
                                    )
                                    .pipe(fs.createWriteStream(filePath));
                            });
                        },
                        (err) => {
                            dumpExportFailureCounter.inc({
                                module: moduleKeys.serviceRequests,
                            });
                            dumpExportsCounter.inc({
                                status: dumpExportStatus.failure,
                            });
                            this.fatalDbError(resolve, err);
                        }
                    );
            } catch (error) {
                dumpExportFailureCounter.inc({
                    module: moduleKeys.serviceRequests,
                });
                dumpExportsCounter.inc({
                    status: dumpExportStatus.failure,
                });
                this.fatalDbError(resolve, error);
            }
        });
    }

    assignSrvcPrvdr(
        query,
        entry_id,
        srvc_provider,
        is_customer_access = 0,
        cust_org_id = 0
    ) {
        //check if srvc_provider is undefined and is_customer_access is 1 than assign user org_id as a srvc_provider
        if (srvc_provider == undefined && is_customer_access == '1') {
            srvc_provider = users_model.getOrgId(this.userContext);
        }
        return new Promise((resolve, reject) => {
            let assign_prvdr_query = {};
            assign_prvdr_query['new_prvdr'] = srvc_provider;

            this.createOrUpdate(
                assign_prvdr_query,
                entry_id,
                is_customer_access,
                cust_org_id
            ).then((operationResp) => {
                resolve(operationResp);
            });
        });
    }

    generateFeedbackLink(query, entry_id) {
        return new Promise((resolve, reject) => {
            // console.log('feedbackUrl',feedbackUrl);
            this.getSingleEntry(query, entry_id).then((operationResp) => {
                if (operationResp.isSuccess()) {
                    let display_code = JSON.parse(operationResp.resp).form_data
                        .title;
                    query['cust_mobile'] = JSON.parse(
                        operationResp.resp
                    )?.form_data?.form_data?.cust_mobile;
                    query['cust_full_name'] = JSON.parse(
                        operationResp.resp
                    )?.form_data?.form_data?.cust_full_name;
                    let feedbackToken = commonUtils.genJti();
                    let token_update_query = {};
                    token_update_query['feedback_token'] = feedbackToken;
                    this.createOrUpdate(token_update_query, entry_id).then(
                        (operationResp) => {
                            if (operationResp.isSuccess()) {
                                query['display_code'] = display_code;
                                // console
                                let linkParams = {
                                    o: users_model.getOrgId(this.userContext),
                                    t: this.srvcTypeId,
                                    display_code: display_code, // needs to be taken from token creation db method
                                    token: feedbackToken,
                                };
                                let domain =
                                    query['host_d'] || process.env.FRONTEND_URL;
                                let feedbackUrl =
                                    domain +
                                    '/customer-feedback?' +
                                    new URLSearchParams(linkParams).toString();
                                console.log('feedbackUrl', feedbackUrl);
                                resolve(feedbackUrl);
                            } else {
                                resolve(undefined);
                            }
                        }
                    );
                } else {
                    resolve(undefined);
                }
            });
        });
    }

    getOverviewProto(query, is_customer_access = 0, callMainDb = false) {
        return new Promise((resolve, reject) => {
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['srvc_type_id'] = this.srvcTypeId || query?.srvc_type_id;
            console.log(
                'service_model getOverviewProto this.srvcTypeId',
                this.srvcTypeId
            );
            console.log(
                'service_model getOverviewProto query srvc_type_id',
                query?.srvc_type_id
            );
            console.log('service_model getOverviewProto query', query);
            // console.log("Trying to get overview data for ",this.srvcTypeId );
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['is_customer_access'] = is_customer_access;
            var form_data = JSON.stringify(query);
            console.log(
                'service_model getOverviewProto form_data',
                form_data,
                filters_
            );
            // console.log(form_data);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            let dbObj;
            if (callMainDb) {
                dbObj = this.db;
            } else {
                dbObj = this.dbReplica || this.db;
                if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }
            }
            dbObj.tms_get_services_overview_proto(form_data, filters_).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_services_overview_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getSingleEntry(query, entry_id = 0) {
        return new Promise((resolve, reject) => {
            // resolve(
            //     new sampleOperationResp(false,
            //         JSON.stringify({}),
            //         HttpStatus.StatusCodes.OK)
            // );
            // return;
            // console.log("Trying to get overview data for ",this.srvcTypeId );
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_type_id'] = query.srvc_type_id || this.srvcTypeId;
            query['entry_id'] = entry_id;

            var form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            console.log('Form db', form_data);
            this.db.tms_get_srvc_req_details(form_data).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(res[0].tms_get_srvc_req_details);

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        try {
                            this.getViewDataFrForm(query).then(
                                (operationResp) => {
                                    // console.log(dbResp.data);
                                    if (operationResp.isSuccess()) {
                                        var finalResp = JSON.parse(
                                            operationResp.resp
                                        );
                                        // console.log(dbResp.data);
                                        finalResp.form_data = dbResp.data;
                                        resolve(
                                            new sampleOperationResp(
                                                true,
                                                JSON.stringify(finalResp),
                                                HttpStatus.StatusCodes.OK
                                            )
                                        );
                                    } else {
                                        resolve(operationResp);
                                    }
                                }
                            );
                        } catch (error) {
                            console.log(
                                'getViewDataFrForm failed ==> ',
                                query,
                                error
                            );
                        }
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getSrvcReqClosedSubtasksEntrys(query, entry_id = 0) {
        return new Promise((resolve, reject) => {
            // resolve(
            //     new sampleOperationResp(false,
            //         JSON.stringify({}),
            //         HttpStatus.StatusCodes.OK)
            // );
            // return;
            // console.log("Trying to get overview data for ",this.srvcTypeId );
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_type_id'] = query.srvc_type_id || this.srvcTypeId;
            query['entry_id'] = entry_id;

            var form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            console.log('getSrvcReqClosedSubtasksEntrys Form db', form_data);
            this.db.tms_get_srvc_req_closed_sbtsk_details(form_data).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(
                        res[0].tms_get_srvc_req_closed_sbtsk_details
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    checkFrDataNeedToSync(prevProfitAndLossData, parsedLambdaResp, skuChanged) {
        const lambdasData = JSON.parse(parsedLambdaResp.body || {});

        if (skuChanged) {
            return false;
        }

        for (const singleRevenueMasterData of lambdasData?.revenue_master_data) {
            const skuKey = singleRevenueMasterData?.Item_SKU;
            if (
                prevProfitAndLossData?.revenue_master_data[
                    `${skuKey}_actual_revenue_fr_sheet`
                ] != singleRevenueMasterData['Actual Price'] ||
                prevProfitAndLossData?.revenue_master_data[
                    `${skuKey}_expected_revenue_fr_sheet`
                ] != singleRevenueMasterData['Expected Price']
            ) {
                return true;
            }
        }

        for (const singleTechnicianMasterData of lambdasData?.technician_master_data) {
            const skuKey = singleTechnicianMasterData?.Item_SKU;
            if (
                prevProfitAndLossData?.technician_master_data[
                    `${skuKey}_technicianCost`
                ] != singleTechnicianMasterData['Technician Cost']
            ) {
                return true;
            }
        }
        return false;
    }

    updateProfitAndLossOfSrvcReq(query, entry_id = 0) {
        return new Promise(async (resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_type_id'] = this.srvcTypeId || query.srvc_type_id;

            const form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            let profitAndLossOfSrvcReqResp;
            try {
                profitAndLossOfSrvcReqResp = await this.getProfitAndLossData(
                    {
                        ...query,
                        sync_data: true,
                    },
                    this.srvcReqId
                );
                if (!profitAndLossOfSrvcReqResp.isSuccess()) {
                    resolve(profitAndLossOfSrvcReqResp);
                    return;
                }
            } catch (error) {
                this.fatalDbError(resolve, error);
            }

            const profitAndLoss = JSON.parse(profitAndLossOfSrvcReqResp.resp);
            try {
                const updateProfitAndLossOfSrvcReqResp =
                    await this.createOrUpdate(
                        {
                            ...query,
                            profit_and_loss: profitAndLoss,
                        },
                        this.srvcReqId
                    );
                resolve(updateProfitAndLossOfSrvcReqResp);
                return;
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }

    getSingleSelectDropdowns = (all_user_fields = []) => {
        return all_user_fields.filter(
            (item) =>
                item.widget === 'select' &&
                (!item.widgetProps || item.widgetProps.mode !== 'multiple')
        );
    };

    getSkuKey(configData, srvcConfigData, requestData, skuFieldKey) {
        // Parse sp_cust_fields_json
        let spCustFields;
        const isSrvcTypeConfig = this.geRevenueColumnConfig(
            this.srvcTypeId,
            configData,
            true
        );

        if (isSrvcTypeConfig) {
            // console.log('srvcTypeConfigData', srvcTypeConfigData);
            spCustFields = JSON.parse(srvcConfigData?.srvc_cust_fields_json);
        } else {
            spCustFields = JSON.parse(configData?.sp_cust_fields_json);
        }
        //const spCustFields = JSON.parse(configData.sp_cust_fields_json);
        let fieldValue;
        // Extract translatedFields
        const translatedFields = spCustFields.translatedFields;
        const selectDropdowns = this.getSingleSelectDropdowns(translatedFields);
        const fieldValueInRequestData = requestData[skuFieldKey];

        const selectDropdown = selectDropdowns.find(
            (singleField) => singleField.key == skuFieldKey
        );

        if (selectDropdown) {
            selectDropdown.options.map((singleOption) => {
                if (singleOption.value == fieldValueInRequestData) {
                    fieldValue = singleOption.label;
                    return;
                }
            });
        } else {
            fieldValue = fieldValueInRequestData;
        }
        return fieldValue;
    }

    /**
     * Retrieves profit and loss data for all service requests in a vertical
     *
     * This function processes profit and loss data for all service requests in a specified vertical.
     * It creates a background job that processes each service request individually and collects the results.
     *
     * @param {number} vertical_id - The ID of the vertical to process
     * @param {Object} query - Query parameters
     * @param {number} [query.org_id] - Organization ID (optional, will use user context if not provided)
     * @param {string} [query.usr_id] - User ID (optional, will create a system user if not provided)
     * @param {boolean} [query.force_update_profit_and_loss=true] - Force update of profit and loss data
     * @param {boolean} [query.sync_data=true] - Synchronize data with external sources
     * @returns {Promise<sampleOperationResp>} A promise that resolves to an operation response
     *   - If successful, returns a 200 status with a message indicating processing has started
     *   - If unsuccessful, returns an appropriate error status and message
     *
     * @example
     * // Get profit and loss data for vertical ID 123
     * const result = await services_model.getProfitAndLossByVertical(123, {
     *   org_id: 456,
     *   force_update_profit_and_loss: true
     * });
     */
    getProfitAndLossByVertical(org_id, vertical_id, query = {}) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] =
                    users_model.getOrgId(this.userContext) || org_id;
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['vertical_id'] = vertical_id;
                query['force_update_profit_and_loss'] = true;
                console.log(
                    'pnlcron debug getProfitAndLossByVertical started',
                    query
                );

                if (!query.usr_id) {
                    console.log('pnlcron debug system user creation');
                    const _userData = {
                        ip_address: this.ip_address,
                        user_agent: this.user_agent_,
                        org_id: org_id,
                    };

                    const userFormData = JSON.stringify(_userData);
                    const userRes =
                        await this.db.tms_get_or_create_system_usr(
                            userFormData
                        );
                    const user_id =
                        userRes[0]?.tms_get_or_create_system_usr?.data?.usr_id;

                    let dummy_req_for_user_context = {
                        uuid: user_id,
                        user_details: {
                            org: {
                                id: org_id,
                            },
                        },
                    };

                    this.user_context = getUserContextFrmReq(
                        dummy_req_for_user_context
                    );

                    query['usr_id'] = user_id;
                    console.log('pnlcron debug system user created', user_id);
                }

                if (!this.db) {
                    console.log('pnlcron debug db not found');
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                // Get service request IDs for the vertical
                const form_data = JSON.stringify(query);

                console.log(
                    'pnlcron debug service request cron started :: ',
                    form_data
                );

                const srvcReqsResult =
                    await this.db.tms_get_srvc_req_ids_by_vertical_id(
                        form_data
                    );

                console.log(
                    'pnlcron debug tms_get_srvc_req_ids_by_vertical_id :: ',
                    srvcReqsResult
                );

                if (!srvcReqsResult || !srvcReqsResult[0]) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Failed to get service requests',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                const dbResp = new db_resp(
                    srvcReqsResult[0].tms_get_srvc_req_ids_by_vertical_id
                );

                console.log('pnlcron debug dbresp for srvc reqs :: ');

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Failed to get service requests',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                // Add jobs to the queue for each service request
                const srvcReqs = dbResp.data || [];
                const jobData = {
                    query,
                    srvcReqs,
                    services_model_data: this.getServicesModelData(this),
                };

                console.log('pnlcron debug queue job data :: ', jobData);

                // Add to queue for processing
                allQueues.WIFY_PROCESS_VERTICAL_PROFIT_LOSS.addJob(jobData);

                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify({
                            message:
                                'Processing started. Results will be available soon.',
                        }),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.log('pnlcron debug catch error :: ', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error,
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getProfitAndLossData(query, entry_id = 0) {
        return new Promise(async (resolve, reject) => {
            const config_data =
                await this.getConfigDataFrSrvcTypeOrVerticalType({ ...query });
            const srvcConfigData = await this.getConfigDataFrSrvcType({});
            let requestData = {};

            if (this.srvcReqId > 0) {
                let srvcReqResp = await this.getSingleEntry(
                    query,
                    this.srvcReqId
                );
                if (!srvcReqResp.isSuccess()) {
                    resolve(srvcReqResp);
                    return;
                }
                requestData = JSON.parse(srvcReqResp.resp)?.form_data
                    ?.form_data;
            }

            console.log('pnlcron debug query', query);
            console.log('pnlcron debug config_data', config_data);
            console.log('pnlcron debug requestData', requestData);
            console.log('pnlcron debug srvcConfigData', srvcConfigData);

            const lambdaResponse = await this.callLambdaFnForProfitAndLossData(
                0,
                { ...query },
                config_data,
                srvcConfigData,
                requestData
            );
            if (!lambdaResponse.isSuccess()) {
                resolve(lambdaResponse);
                return;
            }
            const parsedLambdaResp = JSON.parse(lambdaResponse.resp);

            console.log('pnlcron debug parsedLambdaResp', parsedLambdaResp);

            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_type_id'] = query.srvc_type_id || this.srvcTypeId;
            query['entry_id'] = entry_id || this.srvcReqId;

            const srvcReqProfitAndLossResp =
                await this.getSrvcReqProfitAndLossData(query);
            const parsedSrvcReqProditAndLossResp = JSON.parse(
                srvcReqProfitAndLossResp.resp
            );

            console.log(
                'pnlcron debug parsedSrvcReqProditAndLossResp',
                parsedSrvcReqProditAndLossResp
            );

            let finalResp;
            if (parsedSrvcReqProditAndLossResp?.profit_and_loss_data) {
                //check for values update
                const prevProfitAndLossData =
                    parsedSrvcReqProditAndLossResp?.profit_and_loss_data;

                const latestProfitAndLossData = this.getFinalProfitAndLossResp(
                    parsedLambdaResp,
                    config_data,
                    srvcConfigData,
                    requestData,
                    parsedSrvcReqProditAndLossResp,
                    query?.sync_data
                );

                console.log(
                    'pnlcron debug latestProfitAndLossData',
                    latestProfitAndLossData
                );

                //look if the lambda values are changes between prevProfitAndLossData and latestProfitAndLossData
                const valuesAreChanged = this.checkFrDataNeedToSync(
                    prevProfitAndLossData,
                    parsedLambdaResp,
                    latestProfitAndLossData?.skuChanged
                );

                //checking if the profit and loss data has changed for cron updates
                const updateProfitAndLossOfSrvcReqRespFrCRON = hasDataChanged(
                    prevProfitAndLossData,
                    latestProfitAndLossData,
                    [
                        'revenue_master_data',
                        'technician_master_data',
                        'technician_data_fr_cost_breakdown',
                    ]
                );

                console.log(
                    'pnlcron debug valuesAreChanged',
                    valuesAreChanged,
                    'updateProfitAndLossOfSrvcReqRespFrCRON',
                    updateProfitAndLossOfSrvcReqRespFrCRON
                );

                //if sku change then save data in db first
                if (latestProfitAndLossData?.skuChanged) {
                    console.log('pnlcron debug running if 1');
                    try {
                        const updateProfitAndLossOfSrvcReqResp =
                            await this.createOrUpdate(
                                {
                                    ...query,
                                    profit_and_loss: latestProfitAndLossData,
                                },
                                this.srvcReqId
                            );
                    } catch (error) {
                        this.fatalDbError(resolve, error);
                    }
                } else if (
                    latestProfitAndLossData?.needToUpdateInDb ||
                    updateProfitAndLossOfSrvcReqRespFrCRON
                ) {
                    console.log('pnlcron debug running elif 1');
                    // parameter for db updates which are not happening
                    try {
                        //  if p&l configured data changed from the req then update record in db
                        const updateProfitAndLossOfSrvcReqResp =
                            await this.createOrUpdate(
                                {
                                    ...query,
                                    profit_and_loss: latestProfitAndLossData,
                                },
                                this.srvcReqId
                            );
                    } catch (error) {
                        this.fatalDbError(resolve, error);
                    }
                }
                finalResp = {
                    ...latestProfitAndLossData,
                    show_sync_option: valuesAreChanged,
                };
            } else {
                try {
                    finalResp = this.getFinalProfitAndLossResp(
                        parsedLambdaResp,
                        config_data,
                        srvcConfigData,
                        requestData,
                        parsedSrvcReqProditAndLossResp,
                        true
                    );

                    const updateProfitAndLossOfSrvcReqResp =
                        await this.createOrUpdate(
                            {
                                ...query,
                                profit_and_loss: finalResp,
                            },
                            this.srvcReqId
                        );
                } catch (error) {
                    this.fatalDbError(resolve, error);
                }
            }

            resolve(
                new sampleOperationResp(
                    true,
                    JSON.stringify(finalResp),
                    HttpStatus.StatusCodes.OK
                )
            );
            return;
        });
    }

    getSpPriceConfigForSrvcTypeId = (srvcTypePriceConfig, lineItemGroupKey) => {
        let prcConfigDatafrSingleSrvcType = [];
        let priceConfigFrSingleSrvcTypeId =
            srvcTypePriceConfig[lineItemGroupKey];
        if (
            priceConfigFrSingleSrvcTypeId &&
            priceConfigFrSingleSrvcTypeId.length > 0
        ) {
            prcConfigDatafrSingleSrvcType =
                priceConfigFrSingleSrvcTypeId.filter(
                    (singlePrcFrSrvcTypeId) =>
                        singlePrcFrSrvcTypeId.srvc_type_id == srvc_type_id
                )?.[0];
        } else {
            prcConfigDatafrSingleSrvcType = priceConfigFrSingleSrvcTypeId;
        }
        return prcConfigDatafrSingleSrvcType;
    };

    getLineItemMstPriceFormSrvcConfig = (
        lineItemGroupKey,
        srvcConfigData,
        isBrand
    ) => {
        let lineItemMasterPrice = '';
        let srvcTypePriceConfig =
            srvcConfigData?.srvc_type_pricing_config_for_line_item;

        srvcTypePriceConfig =
            srvcTypePriceConfig ||
            srvcConfigData?.srvc_type_pricing_config_for_line_item;
        if (srvcTypePriceConfig) {
            srvcTypePriceConfig = JSON.parse(srvcTypePriceConfig);
            let lineItemPriceConfigFrGrp = !isBrand
                ? this.getSpPriceConfigForSrvcTypeId(
                      srvcTypePriceConfig,
                      lineItemGroupKey
                  )
                : srvcTypePriceConfig[lineItemGroupKey];
            if (
                lineItemPriceConfigFrGrp &&
                lineItemPriceConfigFrGrp.length > 0
            ) {
                let lineItemPriceConfigkey =
                    'line_item_' + lineItemGroupKey + '_master_rate';
                lineItemMasterPrice =
                    lineItemPriceConfigFrGrp?.[lineItemPriceConfigkey];
            } else {
                if (isBrand) {
                    let lineItemPriceConfigFrGrp =
                        srvcTypePriceConfig[lineItemGroupKey];
                    let lineItemPriceConfigkey =
                        'line_item_' + lineItemGroupKey + '_master_rate';
                    lineItemMasterPrice =
                        lineItemPriceConfigFrGrp?.[lineItemPriceConfigkey];
                } else {
                    let lineItemPriceConfigkey =
                        'line_item_' + lineItemGroupKey + '_master_rate';
                    lineItemMasterPrice =
                        lineItemPriceConfigFrGrp?.[lineItemPriceConfigkey];
                }
            }
        }
        return lineItemMasterPrice;
    };

    getLineItemSelectPriceFormSrvcConfig = (
        lineItemGroup,
        singleLineItem,
        srvcConfigData
    ) => {
        const isBrand = true;
        let fields = [];
        try {
            fields = JSON.parse(lineItemGroup?.fields)?.translatedFields || [];
        } catch (err) {
            fields = [];
        }
        let lineItemSelectPrice = '';
        let priceList = [];
        let masterPrice = this.getLineItemMstPriceFormSrvcConfig(
            lineItemGroup?.key,
            srvcConfigData,
            isBrand
        );
        if (masterPrice) {
            priceList.push(masterPrice);
        }
        let srvcTypePriceConfig =
            srvcConfigData?.srvc_type_pricing_config_for_line_item;

        if (srvcTypePriceConfig && fields && fields.length > 0) {
            fields.forEach((singleField) => {
                let selectedSelectKey = singleLineItem[singleField.key];
                if (selectedSelectKey) {
                    if (typeof srvcTypePriceConfig != 'object') {
                        srvcTypePriceConfig = JSON.parse(srvcTypePriceConfig);
                    }
                    let lineItemPriceConfigFrGrp = !isBrand
                        ? this.getSpPriceConfigForSrvcTypeId(
                              srvcTypePriceConfig,
                              lineItemGroup?.key
                          )
                        : srvcTypePriceConfig[lineItemGroup?.key];

                    lineItemSelectPrice =
                        lineItemPriceConfigFrGrp?.[selectedSelectKey];
                    if (lineItemSelectPrice) {
                        priceList.push(lineItemSelectPrice);
                    }
                }
            });
        }
        // console.log("priceList",priceList)
        lineItemSelectPrice = this.getPriceBasedOnDeterminationEngineRule(
            lineItemGroup,
            priceList,
            srvcConfigData
        );
        return lineItemSelectPrice;
    };

    getPriceBasedOnDeterminationEngineRule = (
        lineItemGroup,
        priceList,
        srvcConfigData
    ) => {
        let PriceBaseOnDeterminationEngineRule = '';
        let prc_config_determination_engine_key =
            'srvc_type_' +
            lineItemGroup?.key +
            '_pricing_config_determination_engine';
        if (srvcConfigData) {
            let prc_config_determination_engine_rule =
                srvcConfigData[prc_config_determination_engine_key];

            if (priceList && priceList.length > 0 && priceList != '') {
                if (prc_config_determination_engine_rule == 'lowest') {
                    PriceBaseOnDeterminationEngineRule = priceList?.reduce(
                        (singlepriceList, value) =>
                            Math.min(singlepriceList, value)
                    );
                } else if (prc_config_determination_engine_rule == 'highest') {
                    PriceBaseOnDeterminationEngineRule = priceList?.reduce(
                        (singlepriceList, value) =>
                            Math.max(singlepriceList, value)
                    );
                } else if (
                    prc_config_determination_engine_rule == 'aggregate'
                ) {
                    let total = priceList?.reduce(function (
                        singlepriceList,
                        value
                    ) {
                        return singlepriceList + value;
                    }, 0);
                    PriceBaseOnDeterminationEngineRule =
                        (total / priceList.length)?.toFixed(2) || 0;
                }
            }
        }
        return PriceBaseOnDeterminationEngineRule;
    };

    getLineItemsTotal = (spPayoutsData) => {
        let total = 0;
        Object.keys(spPayoutsData?.form_data).forEach((singleGroupId) => {
            const groupData = spPayoutsData.form_data[singleGroupId] || [];
            let grpTotal = 0;
            groupData.forEach((singleItem) => {
                grpTotal += parseFloat(numOr0(singleItem.total));
            });
            total += grpTotal;
        });
        return total;
    };

    getLineItemsTotalQty = (spPayoutsData) => {
        let totalQty = 0;
        if (spPayoutsData) {
            Object.keys(spPayoutsData.form_data).map((singleGroupId) => {
                const groupData = spPayoutsData.form_data[singleGroupId] || [];
                let singleGrpTotalQty = 0;
                groupData.forEach((singleItem) => {
                    singleGrpTotalQty += parseFloat(singleItem.qty) || 0;
                });
                totalQty = totalQty + singleGrpTotalQty;
            });
        }
        return totalQty;
    };

    getLineItemsSingleGroupWiseTotalQty = (
        spPayoutsData,
        lineItemGroupKey = undefined
    ) => {
        let lineItemsSingleGroupWiseTotalQtyObj = {};
        if (spPayoutsData) {
            Object.keys(spPayoutsData.form_data).map((singleGroupId) => {
                let key = `${singleGroupId}_total_qty`;
                if (
                    (lineItemGroupKey && lineItemGroupKey == singleGroupId) ||
                    !lineItemGroupKey
                ) {
                    const groupData =
                        spPayoutsData.form_data[singleGroupId] || [];
                    let singleGrpTotalQty = 0;
                    groupData.forEach((singleItem) => {
                        singleGrpTotalQty =
                            singleGrpTotalQty + numOr0(singleItem.qty);
                    });
                    lineItemsSingleGroupWiseTotalQtyObj[key] =
                        singleGrpTotalQty;
                }
            });
        }
        return { ...lineItemsSingleGroupWiseTotalQtyObj };
    };

    getSpPayoutsConfig(configData) {
        // console.log('config_data->>>',configData)
        const spPayoutConfig = configData?.srvc_type_sp_payouts_config;
        return spPayoutConfig ? JSON.parse(spPayoutConfig) : {};
    }

    getRowDataFrVendorCost = (lineItemGroup, spPayoutData, spConfigData) => {
        let lineItemsFrGrp = spPayoutData?.form_data[lineItemGroup.key] || [{}];

        return lineItemsFrGrp;
    };

    getMandayPriceBasedOnDeterminationEngineRule = (
        srvcConfigData,
        mandayPriceList
    ) => {
        let PriceBaseOnDeterminationEngineRule = 0;
        let prc_config_determination_engine_key =
            'srvc_type_manday_pricing_config_determination_engine';
        if (srvcConfigData) {
            let prc_config_determination_engine_rule =
                srvcConfigData?.[prc_config_determination_engine_key];
            if (mandayPriceList && mandayPriceList?.length > 0) {
                if (prc_config_determination_engine_rule == 'lowest') {
                    PriceBaseOnDeterminationEngineRule =
                        mandayPriceList?.reduce((singlepriceList, value) =>
                            Math.min(singlepriceList, value)
                        );
                } else if (prc_config_determination_engine_rule == 'highest') {
                    PriceBaseOnDeterminationEngineRule =
                        mandayPriceList?.reduce((singlepriceList, value) =>
                            Math.max(singlepriceList, value)
                        );
                } else if (
                    prc_config_determination_engine_rule == 'aggregate'
                ) {
                    let total = mandayPriceList?.reduce(function (
                        singlepriceList,
                        value
                    ) {
                        return singlepriceList + value;
                    }, 0);
                    PriceBaseOnDeterminationEngineRule =
                        (total / mandayPriceList.length)?.toFixed(2) || 0;
                }
            }
        }
        return PriceBaseOnDeterminationEngineRule;
    };

    getMandayPrice = (
        srvcConfigData,
        serviceReqFormData,
        isServiceProviderTab
    ) => {
        let mandayPrice = 0;
        let finalMandayPrcList = [];
        let srvcTypePrcConfigFrManday =
            srvcConfigData?.srvc_type_pricing_config_for_manday;
        if (srvcTypePrcConfigFrManday) {
            let srvc_type_id = serviceReqFormData?.srvc_type_id;
            let locGrpWisePriceConfigFrManday = JSON.parse(
                srvcTypePrcConfigFrManday
            )?.srvc_type_pricing_config_for_manday;
            locGrpWisePriceConfigFrManday = isServiceProviderTab
                ? locGrpWisePriceConfigFrManday?.filter(
                      (singleValue) => singleValue.srvc_type_id == srvc_type_id
                  )?.[0]
                : locGrpWisePriceConfigFrManday;

            let locGroupIds = serviceReqFormData?.location_grp_ids;
            finalMandayPrcList.push(
                locGrpWisePriceConfigFrManday?.manday_master_rate || 0
            );
            if (locGrpWisePriceConfigFrManday) {
                Object.keys(locGrpWisePriceConfigFrManday).map(
                    (singleConfigLocGrp) => {
                        let matchedLocGrp = locGroupIds?.find(
                            (singleLocGrpId) =>
                                singleLocGrpId == singleConfigLocGrp
                        );
                        if (
                            matchedLocGrp &&
                            locGrpWisePriceConfigFrManday[matchedLocGrp]
                        ) {
                            finalMandayPrcList.push(
                                locGrpWisePriceConfigFrManday[matchedLocGrp]
                            );
                        }
                    }
                );
            }
        }
        mandayPrice = this.getMandayPriceBasedOnDeterminationEngineRule(
            srvcConfigData,
            finalMandayPrcList
        );
        // console.log("mandayPrice",mandayPrice);
        return mandayPrice;
    };

    getSrvcReqLineItemConfigData = (srvcConfigData) => {
        return srvcConfigData?.srvc_type_line_item_config
            ? JSON.parse(srvcConfigData?.srvc_type_line_item_config) || {}
            : {};
    };

    getLineItemTypeName = (srvcConfigData, singleLineItemData) => {
        const srvcReqLineItemConfig =
            this.getSrvcReqLineItemConfigData(srvcConfigData);
        return srvcReqLineItemConfig[singleLineItemData]?.label;
    };

    getSingleLineItemTypeConfigData = ({ srvcConfigData, lineItemKey }) => {
        const srvcReqLineItemConfig =
            this.getSrvcReqLineItemConfigData(srvcConfigData);
        return srvcReqLineItemConfig[lineItemKey];
    };

    getSingleLineItemTypeFields = ({ srvcConfigData, lineItemKey }) => {
        const srvcReqLineItemConfig =
            this.getSrvcReqLineItemConfigData(srvcConfigData);
        return srvcReqLineItemConfig[lineItemKey]?.fields;
    };

    getLineItemsRowData = (srvcConfigData, srvcReqLineItemsData) => {
        let rowData = [];
        const srvcReqLineItemConfig =
            this.getSrvcReqLineItemConfigData(srvcConfigData);
        Object.keys(srvcReqLineItemConfig).map((singleGroupId) => {
            const singleGroup = srvcReqLineItemConfig?.[singleGroupId];
            let nameFieldFormula = singleGroup?.name_field_formula;
            let fields = [];
            try {
                fields = JSON.parse(singleGroup.fields).translatedFields || [];
            } catch (err) {
                fields = [];
            }

            const idVsLabelMapping = {};
            fields?.forEach((singleFieldMeta) => {
                idVsLabelMapping[singleFieldMeta.label] = singleFieldMeta.key;
            });

            let srvcReqLineItemData =
                srvcReqLineItemsData?.form_data?.[singleGroup.key];
            if (srvcReqLineItemData && srvcReqLineItemData.length > 0) {
                srvcReqLineItemData.forEach(
                    (singleSrvcReqLineItemData, index) => {
                        let label = `Item ${index + 1}`;
                        let valueDataFrLineItem =
                            commonUtils.getValueDataFrmFormMeta(
                                fields,
                                singleSrvcReqLineItemData
                            );
                        if (nameFieldFormula && nameFieldFormula.length > 0) {
                            label = parseFormulaToString(
                                nameFieldFormula,
                                idVsLabelMapping,
                                valueDataFrLineItem
                            );
                        }
                        let qty = numOr0(singleSrvcReqLineItemData?.qty);
                        let rate = numOr0(singleSrvcReqLineItemData?.rate);
                        let total = qty * rate;
                        if (total > 0) {
                            let billingLineItemObj = {
                                input_table_id:
                                    singleSrvcReqLineItemData.input_table_id,
                                type: this.getLineItemTypeName(
                                    srvcConfigData,
                                    singleGroup.key
                                ),
                                item: label,
                                unit:
                                    singleGroup?.quantity_field_label || 'Qty',
                                qty: qty,
                                rate: rate,
                            };
                            rowData.push(billingLineItemObj);
                        }
                    }
                );
            }
        });
        return rowData;
    };

    getAdditionalChargesName = (srvcConfigData) => {
        let additionalConfigDataFields =
            this.getAdditionalConfigData(srvcConfigData)?.additional_line_item
                ?.fields;

        try {
            const parsed = JSON.parse(
                additionalConfigDataFields
            )?.translatedFields;
            return parsed?.[0]?.options || [];
        } catch (err) {
            return [];
        }
    };

    getAdditionalConfigData = (srvcConfigData) => {
        let config = srvcConfigData?.srvc_type_additional_billing_config;
        return config ? JSON.parse(config) : {};
    };

    getAdditionalQtyFieldLabel = (srvcConfigData) => {
        const configData = srvcConfigData?.srvc_type_additional_billing_config;

        if (!configData) return; // Avoid parsing undefined or null

        try {
            return JSON.parse(configData)?.additional_line_item
                ?.quantity_field_label;
        } catch (error) {
            console.log(
                'service_model :: getAdditionalQtyFieldLabel :: Invaild JSON ',
                error
            );
            return undefined; // Return undefined if JSON parsing fails
        }
    };

    getAdditionalLineItemsRowData = (
        srvcConfigData,
        srvcReqAdditionalLineItemsData
    ) => {
        let rowData = [];
        let additionalLineItem =
            srvcReqAdditionalLineItemsData?.form_data?.additional_line_item;
        if (additionalLineItem) {
            additionalLineItem.forEach((singleAdditionalLineItem) => {
                let itemName = undefined;
                Object.values(singleAdditionalLineItem).forEach(
                    (singleAdditionalLineItemVal) => {
                        let additionalChargesName =
                            this.getAdditionalChargesName(
                                srvcConfigData
                            )?.filter(
                                (singleAdditioanlChargesName) =>
                                    singleAdditioanlChargesName.value ==
                                    singleAdditionalLineItemVal
                            )?.[0]?.label;

                        if (additionalChargesName) {
                            itemName = additionalChargesName;
                        }
                    }
                );

                let qty = 0;
                if (
                    singleAdditionalLineItem?.qty !== undefined &&
                    singleAdditionalLineItem?.qty !== null
                ) {
                    // Check if it's a valid number
                    const qtyAsNumber = Number(singleAdditionalLineItem.qty);
                    if (!isNaN(qtyAsNumber)) {
                        // If it's a valid number, use the toFixed method
                        qty = qtyAsNumber.toFixed(2);
                    }
                }
                let rate = singleAdditionalLineItem?.rate?.toFixed(2) || 0;
                let total = singleAdditionalLineItem?.total?.toFixed(2) || 0;
                if (total && total != 0) {
                    let additionalLineItemObj = {
                        input_table_id: singleAdditionalLineItem.input_table_id,
                        type: 'Additional-Expense',
                        item: itemName,
                        unit:
                            this.getAdditionalQtyFieldLabel(srvcConfigData) ||
                            'Qty',
                        qty: qty,
                        rate: rate,
                    };
                    rowData.push(additionalLineItemObj);
                }
            });
        }
        return rowData;
    };

    getMandayRowData = (
        srvcConfigData,
        serviceReqFormData,
        calendarData,
        isServiceProviderTab = false
    ) => {
        let mandaysRowData = [];

        let day_wise_billing_data =
            serviceReqFormData?.form_data?.['sp_day_wise_billing_data'];

        //Get calendar_data by org_type
        let calendar_data = calendarData?.calendar_data?.filter(
            (singleCalenderData) =>
                singleCalenderData.org_type == 'ORG_TYPE_SRVC_PRVDR'
        )?.[0]?.srvc_req_calendar_data;

        if (calendar_data) {
            calendar_data.forEach((singleCalenderData) => {
                singleCalenderData['charge_basis'] =
                    day_wise_billing_data?.[
                        singleCalenderData.day
                    ]?.charge_basis;
            });

            let mandayCalendarData = calendar_data?.filter(
                (singleValue) => singleValue.charge_basis == 'manday'
            );
            if (mandayCalendarData && mandayCalendarData.length > 0) {
                let billingMandayObj = {
                    input_table_id: 'manday_billing_items',
                    type: 'Manday',
                    item: 'Manday',
                    unit: 'Mandays',
                    qty: mandayCalendarData?.length || 0,
                    rate: this.getMandayPrice(
                        srvcConfigData,
                        serviceReqFormData,
                        isServiceProviderTab
                    ),
                };
                mandaysRowData.push(billingMandayObj);
            }
        }
        // console.log("mandayCalendarData",mandaysRowData);
        return mandaysRowData;
    };

    getFinalBillingRowData = (
        srvcConfigData,
        srvcReqLineItemsData,
        srvcReqAdditionalLineItemsData,
        srvcReqDeductionData,
        srvcReqFinalBillingData,
        serviceReqFormData,
        calendarData,
        isServiceProviderTab,
        hideDeductions
    ) => {
        const finalBillingData = srvcReqFinalBillingData?.form_data || {};
        // generate data as per line items and additonal line items with config
        let lineItemsRowData =
            this.getLineItemsRowData(srvcConfigData, srvcReqLineItemsData) ||
            [];
        let additionalLineItemsRowsData =
            this.getAdditionalLineItemsRowData(
                srvcConfigData,
                srvcReqAdditionalLineItemsData
            ) || [];
        // let deductionLineItemsRowsData =
        //     getDeductionlLineItemsRowData(srvcConfigData, srvcReqDeductionData) ||
        //     [];
        let mandaysRowData =
            this.getMandayRowData(
                srvcConfigData,
                serviceReqFormData,
                calendarData,
                true
            ) || [];

        let finalBillingItemsRowData = [];
        let billing_type = srvcConfigData?.srvc_type_billing_type;

        if (billing_type == 'line_item') {
            finalBillingItemsRowData = [
                ...lineItemsRowData,
                ...additionalLineItemsRowsData,
                //...(hideDeductions ? [] : deductionLineItemsRowsData),
            ];
        } else if (billing_type == 'manday') {
            finalBillingItemsRowData = [
                ...mandaysRowData,
                ...additionalLineItemsRowsData,
                //...(hideDeductions ? [] : deductionLineItemsRowsData),
            ];
        } else if (billing_type == 'hybrid') {
            finalBillingItemsRowData = [
                ...lineItemsRowData,
                ...mandaysRowData,
                ...additionalLineItemsRowsData,
                //...(hideDeductions ? [] : deductionLineItemsRowsData),
            ];
        } else {
            finalBillingItemsRowData = [
                ...lineItemsRowData,
                ...mandaysRowData,
                ...additionalLineItemsRowsData,
                //...(hideDeductions ? [] : deductionLineItemsRowsData),
            ];
        }
        // console.log(
        //     'services_model :: getFinalBillingRowData :: finalBillingItemsRowData',
        //     finalBillingItemsRowData
        // );
        finalBillingItemsRowData.forEach((singleRow) => {
            let qty = singleRow?.qty;
            if (typeof qty === 'number') {
                qty = qty.toFixed(2);
            }
            let rate = singleRow?.rate;
            let sub_total = (qty * rate)?.toFixed(2);

            // prefill from srvcReqFinalBillingData
            let prefillData = finalBillingData[singleRow.input_table_id] || {};
            singleRow['qty_discount'] = prefillData['qty_discount'] || '';
            singleRow['rate_discount'] = prefillData['rate_discount'] || '';

            let qty_discount = numOr0(singleRow.qty_discount);
            let rate_discount = numOr0(singleRow.rate_discount);
            let qty_after_discount = (qty - qty_discount)?.toFixed(2);
            let rate_after_discount = (rate - rate_discount)?.toFixed(2);
            let sub_total_after_discount = (
                qty_after_discount * rate_after_discount
            )?.toFixed(2);
            let discount_rs = (sub_total - sub_total_after_discount)?.toFixed(
                2
            );

            singleRow['qty_after_discount'] = qty_after_discount || 0;
            singleRow['rate_after_discount'] = rate_after_discount || 0;
            singleRow['sub_total'] = sub_total || 0;
            singleRow['sub_total_after_discount'] =
                sub_total_after_discount || 0;
            singleRow['discount_rs'] = discount_rs || 0;
        });
        return finalBillingItemsRowData;
    };

    /**
     * Get project profit and loss data
     * This method directly calls the database function to get project profit and loss data
     */
    getProjectProfitAndLossData(query, entry_id = 0) {
        return new Promise(async (resolve, reject) => {
            query['srvc_type_id'] = this.srvcTypeId || query?.srvc_type_id;
            query['srvc_req_id'] = this.srvcReqId || entry_id;
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['entry_id'] = this.srvcReqId || entry_id;
            const spConfigData =
                await this.getConfigDataFrSrvcTypeOrVerticalType({ ...query });
            const getSrvcReqCalendarData =
                await this.getSrvcReqCalendarData(query);
            const calendarData = JSON.parse(getSrvcReqCalendarData.resp);
            const srvcReqResp = await this.getSingleEntry({}, this.srvcReqId);
            const srvcReqFormData = JSON.parse(srvcReqResp.resp);
            const spPayoutConfig = await this.getSpPayoutsConfig(spConfigData);
            const spPayoutData = JSON.parse(srvcReqResp.resp)?.form_data
                .form_data.sp_payouts_data;
            let vendorCostData;
            if (spConfigData?.enable_sp_payouts) {
                vendorCostData = this.getVendorCostData(
                    spPayoutConfig,
                    spPayoutData
                );
            }

            const form_data = JSON.stringify(query);
            let revenueBreakdownData;
            let additionalDiscount;
            if (spConfigData?.srvc_type_enable_billing) {
                revenueBreakdownData = this.getFinalBillingRowData(
                    spConfigData,
                    srvcReqFormData?.form_data.form_data.sp_line_items,
                    srvcReqFormData?.form_data.form_data
                        .sp_additional_billing_items,
                    srvcReqFormData?.form_data.form_data.sp_deduction_data,
                    srvcReqFormData?.form_data.form_data.sp_final_billing_items,
                    srvcReqFormData?.form_data,
                    calendarData
                );
                additionalDiscount =
                    srvcReqFormData.form_data?.form_data
                        ?.sp_final_additional_dis;
            }

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }

            this.db.tms_get_project_profit_and_loss_data(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_project_profit_and_loss_data
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        let parsedData = {};
                        try {
                            parsedData =
                                typeof dbResp.data === 'string'
                                    ? JSON.parse(dbResp.data)
                                    : dbResp.data;
                        } catch (e) {
                            console.log(
                                'services_model :: getProjectProfitAndLossData :: error',
                                e
                            );
                            parsedData = {};
                        }

                        parsedData.revenueBreakdownData = revenueBreakdownData;
                        parsedData.vendorCostData = vendorCostData;
                        parsedData.additionalDiscount = additionalDiscount;

                        // Return the enriched response
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(parsedData),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getVendorCostData(spPayoutConfig, spPayoutData) {
        let getRowData;

        try {
            Object.keys(spPayoutConfig).forEach((singleGroupId) => {
                const singleGroup = spPayoutConfig[singleGroupId];
                getRowData = this.getRowDataFrVendorCost(
                    singleGroup,
                    spPayoutData,
                    spPayoutConfig
                );
            });
        } catch (error) {
            console.log('services_model :: getVendorCostData :: error', error);
            getRowData = null;
        }

        return getRowData;
    }

    createOrUpdateBatch(
        query,
        is_customer_access = 0,
        is_api_call = 0,
        is_bulk_update = 0
    ) {
        // console.log("Query rxd : ",JSON.stringify(query));
        return new Promise((resolve, reject) => {
            // resolve(
            //     new sampleOperationResp(false,
            //         "Got Data - " + JSON.stringify(batch_data),
            //         HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
            // );
            // return;
            // console.log("form_data",form_data);
            // var validationResp = this.validateCreateNewForm(query);
            // if(!validationResp.isSuccess()){
            //     resolve(
            //         validationResp
            //     );
            //     return;
            // }
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_type_id'] = this.srvcTypeId;
            query['batch_data'] = query.batch_data;
            query['is_customer_access'] = is_customer_access;
            query['is_api_call'] = is_api_call;
            query['is_bulk_update'] = is_bulk_update;
            // console.log(this.srvcTypeId);
            // resolve(
            //     new sampleOperationResp(false,
            //         `Got Data - ${entry_id} - `  + JSON.stringify(query),
            //         HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
            // );
            // return;
            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            // console.log("Form data to tms_create_srvc_reqs_batch",form_data);
            this.db.tms_create_srvc_reqs_batch(form_data).then(
                (res) => {
                    var dbResp = new db_resp(res[0].tms_create_srvc_reqs_batch);

                    if (dbResp.code == 'title_or_key_exists') {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Title or Key not unique',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (
                        !dbResp.status &&
                        dbResp.code != 'Internal_error'
                    ) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                dbResp.code,
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else if (is_bulk_update && dbResp.status) {
                        let workflow = getServiceWorkflowModel(this);
                        workflow.trigger(
                            query,
                            0,
                            dbResp,
                            is_customer_access,
                            this.db
                        );
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    } else {
                        let workflow = getServiceWorkflowModel(this);
                        workflow.trigger(
                            query,
                            0,
                            dbResp,
                            is_customer_access,
                            this.db
                        );

                        if ((is_api_call = 1)) {
                            let apiRespObj = {
                                status: 'success',
                                message: 'Orders created/updated successfully',
                                data: {
                                    resp: this.helperCreateAPIRespFrOrders(
                                        dbResp.data
                                    ),
                                    service_type_id: this.srvcTypeId + '',
                                },
                            };
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(apiRespObj),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                            return;
                        }

                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getLineItemsWithRevisions({ lineItemsData }) {
        const newLineItemsData = { ...lineItemsData };
        if (newLineItemsData.revisions == undefined) {
            newLineItemsData['revisions'] = [];
        }
        // create a revision
        const newRevisions = {
            c_by: users_model.getUUID(this.userContext),
            c_name: users_model.getUserName(this.userContext),
            c_time: moment.utc(),
            ...newLineItemsData,
            form_data: newLineItemsData.form_data,
        };
        newLineItemsData['revisions'] = [
            ...newLineItemsData['revisions'],
            newRevisions,
        ];
        return newLineItemsData;
    }

    parseFormulaToValue(
        formula,
        nameToIdMapping = undefined,
        data,
        isSrvcReqLocked = true
    ) {
        let matches = formula.match(/{[^{}]+}/g);
        matches.forEach((matchString) => {
            let word = matchString.substring(1, matchString.length - 1);
            let key = nameToIdMapping[word];
            let fieldValue = key in data ? data[key] : 'undefined';
            formula = formula.replace(`{${word}}`, fieldValue);
        });
        let result = 0;
        try {
            result = eval(formula);
            if (!isSrvcReqLocked) {
                result = parseFloat(result).toFixed(2);
            }
        } catch (error) {
            console.log('parseFormulaToValue error', error);
        }
        return result;
    }

    setNewRowData({
        lineItemGroup,
        newData,
        lineItemsData,
        srvcReqLineItemConfig,
        translatedFields,
        srvcConfigData,
        checkFrLineItemsMasterPrice,
        priceConfigFrLineItem,
        currentRowData,
    }) {
        const fields = translatedFields || [];
        const configFrGrp = srvcReqLineItemConfig;
        // Do Quantity and total calculations
        newData.forEach((singleLineItem) => {
            //rate autoifll based on srvc_config_price.
            //if rate is not define in price_config_master then blank and editable.
            //if rate is define then disable.
            if (
                checkFrLineItemsMasterPrice &&
                currentRowData.input_table_id == singleLineItem.input_table_id
            ) {
                let srvcTypeCustomPriceconfig =
                    this.getLineItemSelectPriceFormSrvcConfig(
                        lineItemGroup,
                        singleLineItem,
                        {
                            ...srvcConfigData,
                            srvc_type_pricing_config_for_line_item:
                                priceConfigFrLineItem,
                        }
                    );
                if (
                    srvcTypeCustomPriceconfig &&
                    singleLineItem.rate !== '' && // reject empty string
                    singleLineItem.rate !== null &&
                    singleLineItem.rate !== undefined &&
                    !isNaN(Number(singleLineItem.rate)) &&
                    singleLineItem.rate != srvcTypeCustomPriceconfig
                ) {
                    lineItemsData['missmatchPriceMaster'] = true;
                }
                if (srvcTypeCustomPriceconfig) {
                    singleLineItem.rate = srvcTypeCustomPriceconfig;
                }
            }
            const { quantity_field_formula } = configFrGrp;
            if (quantity_field_formula && quantity_field_formula.length > 0) {
                const qty = parseFormulaToValue(
                    quantity_field_formula,
                    getIdVsLabelMapping(fields),
                    singleLineItem,
                    false
                );
                singleLineItem.qty = qty;
            }
            singleLineItem.total =
                numOr0(singleLineItem.qty) * numOr0(singleLineItem.rate);
        });

        let newLineItemsData = { ...lineItemsData };
        let form_data = newLineItemsData?.form_data || {};
        form_data[lineItemGroup.key] = newData;
        newLineItemsData['form_data'] = form_data;
        newLineItemsData['total'] = this.getLineItemsTotal(newLineItemsData);
        newLineItemsData['total_qty'] =
            this.getLineItemsTotalQty(newLineItemsData);
        let finalLineItemsSingleGroupWiseTotalQtyObj =
            this.getLineItemsSingleGroupWiseTotalQty(
                newLineItemsData,
                lineItemGroup.key
            );
        newLineItemsData = {
            ...newLineItemsData,
            ...finalLineItemsSingleGroupWiseTotalQtyObj,
        };
        return newLineItemsData;
    }

    checkFrValidData({ srvcReqLineItemConfig, lineItemDataByKey }) {
        let fields = [];
        const singleLineItemDataByKey = lineItemDataByKey[0];

        try {
            fields =
                JSON.parse(srvcReqLineItemConfig?.fields)?.translatedFields ||
                [];
        } catch (err) {
            fields = [];
        }

        if (fields && fields.length > 0) {
            for (const singleField of fields) {
                if (singleLineItemDataByKey[singleField.key]) {
                    return lineItemDataByKey;
                }
            }
        }

        if (!isNaN(Number(singleLineItemDataByKey.qty))) {
            return lineItemDataByKey;
        }
    }
    generateUUID = () => {
        return uuidv4();
    };

    async getFinalBatchData({ query, lineItemKey, srvcConfigData, batchData }) {
        return new Promise(async (resolve, reject) => {
            try {
                let srvcReqDisplayCodes = [];
                batchData.forEach((singleBatchData) => {
                    srvcReqDisplayCodes.push(singleBatchData?.tms_display_code);
                });
                const pageSize = 10;
                const form_data = JSON.stringify(query);
                const srvcReqFormDataBatch = (
                    await this.db.tms_get_srvc_req_batch_data_by_display_code(
                        form_data,
                        srvcReqDisplayCodes
                    )
                )?.[0].tms_get_srvc_req_batch_data_by_display_code;

                const srvcReqFormDataBatchData = srvcReqFormDataBatch.data;

                const srvcReqLineItemConfig =
                    this.getSingleLineItemTypeConfigData({
                        srvcConfigData,
                        lineItemKey,
                    });

                let modifiedBatchData = [];
                const currentBatchTicketIdVsData = {};
                let missmatchPriceMasterRows = [];
                const srvcReqLineItemFields = this.getSingleLineItemTypeFields({
                    srvcConfigData,
                    lineItemKey,
                });
                let translatedFields = [];

                if (srvcReqLineItemFields) {
                    translatedFields = JSON.parse(
                        srvcReqLineItemFields
                    )?.translatedFields;
                }

                for (const [index, singleBatchData] of batchData.entries()) {
                    const singleFormData =
                        srvcReqFormDataBatchData[
                            singleBatchData?.tms_display_code
                        ];
                    const row = index + 1;
                    const existingLineItemsData = singleFormData?.line_items;
                    const { tms_display_code, ...modifiedSingleBatchData } =
                        singleBatchData;
                    const repeatedTicketIdInBatch =
                        currentBatchTicketIdVsData[tms_display_code];

                    const lineItemDataByKey =
                        existingLineItemsData?.form_data?.[lineItemKey];

                    const pricingConfigHis =
                        singleFormData?.srvc_type_history_pricing_config;
                    let priceConfigFrLineItem;

                    let checkFrLineItemsMasterPrice;
                    if (pricingConfigHis && pricingConfigHis?.length > 0) {
                        priceConfigFrLineItem =
                            pricingConfigHis?.[0]
                                ?.srvc_type_pricing_config_for_line_item;

                        if (
                            priceConfigFrLineItem &&
                            priceConfigFrLineItem != ''
                        ) {
                            checkFrLineItemsMasterPrice = true;
                        }
                    }
                    if (modifiedSingleBatchData.input_table_id == undefined) {
                        modifiedSingleBatchData['input_table_id'] =
                            this.generateUUID();
                    }
                    modifiedSingleBatchData['key'] =
                        modifiedSingleBatchData['input_table_id'];

                    if (lineItemDataByKey && !repeatedTicketIdInBatch) {
                        let checkedData;
                        if (lineItemDataByKey?.length == 1) {
                            checkedData = this.checkFrValidData({
                                srvcReqLineItemConfig,
                                lineItemDataByKey,
                            });
                        }
                        const { revisions, ...existingRest } =
                            existingLineItemsData;
                        const newData = this.setNewRowData({
                            lineItemGroup: srvcReqLineItemConfig,
                            newData: [
                                ...(lineItemDataByKey?.length > 1 || checkedData
                                    ? lineItemDataByKey
                                    : []),
                                modifiedSingleBatchData,
                            ],
                            lineItemsData: { ...existingRest } || {},
                            srvcReqLineItemConfig,
                            translatedFields,
                            srvcConfigData,
                            checkFrLineItemsMasterPrice,
                            priceConfigFrLineItem,
                            currentRowData: modifiedSingleBatchData,
                        });
                        if (newData?.missmatchPriceMaster) {
                            missmatchPriceMasterRows.push(row);
                            delete newData['missmatchPriceMaster'];
                        }
                        const fincalLineItemData =
                            this.getLineItemsWithRevisions({
                                lineItemsData: newData,
                            });
                        currentBatchTicketIdVsData[tms_display_code] = {
                            entry_id: singleFormData?.entry_id,
                            tms_display_code,
                            line_items: fincalLineItemData,
                        };

                        modifiedBatchData.push({
                            entry_id: singleFormData?.entry_id,
                            tms_display_code,
                            line_items: fincalLineItemData,
                        });
                    } else if (repeatedTicketIdInBatch) {
                        const { revisions, ...rest } =
                            repeatedTicketIdInBatch?.line_items;
                        const newData = this.setNewRowData({
                            lineItemGroup: srvcReqLineItemConfig,
                            newData: [
                                ...repeatedTicketIdInBatch?.line_items
                                    ?.form_data?.[lineItemKey],
                                modifiedSingleBatchData,
                            ],
                            lineItemsData:
                                {
                                    ...rest,
                                } || {},
                            srvcReqLineItemConfig,
                            translatedFields,
                            srvcConfigData,
                            checkFrLineItemsMasterPrice,
                            priceConfigFrLineItem,
                            currentRowData: modifiedSingleBatchData,
                        });

                        if (newData?.missmatchPriceMaster) {
                            missmatchPriceMasterRows.push(row);
                            delete newData['missmatchPriceMaster'];
                        }

                        const fincalLineItemData =
                            this.getLineItemsWithRevisions({
                                lineItemsData: newData,
                            });
                        currentBatchTicketIdVsData[tms_display_code] = {
                            entry_id: singleFormData?.entry_id,
                            tms_display_code,
                            line_items: fincalLineItemData,
                        };
                    } else {
                        const newData = this.setNewRowData({
                            lineItemGroup: srvcReqLineItemConfig,
                            newData: [modifiedSingleBatchData],
                            lineItemsData: { ...existingLineItemsData } || {},
                            srvcReqLineItemConfig,
                            translatedFields,
                            srvcConfigData,
                            checkFrLineItemsMasterPrice,
                            priceConfigFrLineItem,
                            currentRowData: modifiedSingleBatchData,
                        });

                        if (newData?.missmatchPriceMaster) {
                            missmatchPriceMasterRows.push(row);
                            delete newData['missmatchPriceMaster'];
                        }

                        const fincalLineItemData =
                            this.getLineItemsWithRevisions({
                                lineItemsData: newData,
                            });

                        currentBatchTicketIdVsData[tms_display_code] = {
                            entry_id: singleFormData?.entry_id,
                            tms_display_code,
                            line_items: fincalLineItemData,
                        };
                    }
                }
                if (missmatchPriceMasterRows?.length > 0) {
                    return resolve({
                        missmatchPriceMasterRows,
                        missmatchPriceMaster:
                            missmatchPriceMasterRows.length > 0,
                    });
                }
                modifiedBatchData = Object.values(currentBatchTicketIdVsData);
                return resolve({ modifiedBatchData });
            } catch (error) {
                console.log(
                    'services_model :: getFinalBatchData :: error',
                    error
                );
                this.fatalDbError(resolve, error);
            }
        });
    }

    getPageNoOnTheBaseOfRow({ erroricRowsKey, pageSize }) {
        return Math.ceil(erroricRowsKey / pageSize);
    }
    getRowOnTheBaseOfPageSize({ erroricRowsKey, pageSize }) {
        if (erroricRowsKey > pageSize) {
            return Math.ceil(erroricRowsKey % pageSize)
                ? Math.ceil(erroricRowsKey % pageSize)
                : pageSize;
        }
        return erroricRowsKey;
    }

    getMissmatchPriceMasterError({ missmatchPriceMasterRows }) {
        let hint = '';

        missmatchPriceMasterRows.forEach(
            (singleMissMatchPriceMasterRow, index) => {
                const temp = `Failed row --> error on page ${this.getPageNoOnTheBaseOfRow({ erroricRowsKey: singleMissMatchPriceMasterRow, pageSize: 10 })} in row ${this.getRowOnTheBaseOfPageSize({ erroricRowsKey: singleMissMatchPriceMasterRow, pageSize: 10 })} Price does not match with pricing master`;
                const errorMessage = index > 0 ? `\n${temp}` : `${temp}`;
                hint = hint + errorMessage;
            }
        );
        return {
            message: `Invalid Price value: `,
            hint: hint,
            code: 'INVALID_PRICE_VALUE',
        };
    }

    createOrUpdateSrvcLineItemsBatch(
        query,
        is_customer_access = 0,
        is_api_call = 0,
        is_bulk_line_items_update = 0,
        lineItemKey
    ) {
        // console.log("Query rxd : ",JSON.stringify(query));
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['srvc_type_id'] = this.srvcTypeId;
                query['batch_data'] = query.batch_data;
                query['is_customer_access'] = is_customer_access;
                query['is_api_call'] = is_api_call;
                query['is_bulk_line_items_update'] = is_bulk_line_items_update;

                const srvcConfigData = await this.getConfigDataFrSrvcType({});
                const data = await this.getFinalBatchData({
                    query,
                    lineItemKey,
                    srvcConfigData,
                    batchData: query.batch_data,
                });

                if (data?.missmatchPriceMaster) {
                    const priceMastererror = this.getMissmatchPriceMasterError({
                        missmatchPriceMasterRows:
                            data?.missmatchPriceMasterRows || [],
                    });
                    throw priceMastererror;
                }
                query['batch_data'] = data?.modifiedBatchData || [];
                var form_data = JSON.stringify(query);
                // console.log(
                //     'createOrUpdateSrvcLineItemsBatch form_data',
                //     form_data
                // );
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                this.db.tms_create_srvc_reqs_batch(form_data).then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_create_srvc_reqs_batch
                        );

                        if (dbResp.code == 'title_or_key_exists') {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Title or Key not unique',
                                    HttpStatus.StatusCodes.CONFLICT
                                )
                            );
                        } else if (
                            !dbResp.status &&
                            dbResp.code != 'Internal_error'
                        ) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    dbResp.code,
                                    HttpStatus.StatusCodes.CONFLICT
                                )
                            );
                        } else if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else if (is_bulk_line_items_update && dbResp.status) {
                            let workflow = getServiceWorkflowModel(this);
                            workflow.trigger(
                                query,
                                0,
                                dbResp,
                                is_customer_access,
                                this.db
                            );
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        } else {
                            let workflow = getServiceWorkflowModel(this);
                            workflow.trigger(
                                query,
                                0,
                                dbResp,
                                is_customer_access,
                                this.db
                            );

                            if ((is_api_call = 1)) {
                                let apiRespObj = {
                                    status: 'success',
                                    message:
                                        'Orders created/updated successfully',
                                    data: {
                                        resp: this.helperCreateAPIRespFrOrders(
                                            dbResp.data
                                        ),
                                        service_type_id: this.srvcTypeId + '',
                                    },
                                };
                                resolve(
                                    new sampleOperationResp(
                                        true,
                                        JSON.stringify(apiRespObj),
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                                return;
                            }

                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        console.log(
                            'createOrUpdateSrvcLineItemsBatch :: error',
                            error
                        );
                        this.fatalDbError(resolve, error);
                    }
                );
            } catch (error) {
                console.log('createOrUpdateSrvcLineItemsBatch :: error', error);
                this.fatalDbError(resolve, error);
            }
        });
    }

    createOrUpdateBatchFrVertical(
        query,
        is_cust_req,
        is_customer_access = 0,
        is_api_call = 0
    ) {
        return new Promise(async (resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['batch_data'] = query.batch_data;
                query['is_customer_access'] = is_customer_access;
                query['is_api_call'] = is_api_call;
                query['is_cust_req'] = is_cust_req;

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                const dummyQuery = Object.values(
                    query.batch_data.reduce((acc, obj) => {
                        const { srvc_type_id, ...rest } = obj;
                        const srvc_type_id_str = srvc_type_id.toString(); // Convert srvc_type_id to string
                        if (acc[srvc_type_id_str]) {
                            acc[srvc_type_id_str].batch_data.push(rest);
                        } else {
                            acc[srvc_type_id_str] = {
                                ...query,
                                srvc_type_id: srvc_type_id_str, // Assign srvc_type_id as string
                                batch_data: [rest],
                            };
                        }
                        return acc;
                    }, {})
                );

                let form_data = JSON.stringify(dummyQuery);
                let dbResp = (
                    await this.db.tms_create_srvc_req_fr_verticals_batch(
                        form_data
                    )
                )?.[0].tms_create_srvc_req_fr_verticals_batch;

                if (!dbResp.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            JSON.stringify(dbResp.data),
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                }

                dummyQuery.forEach((singleEntry) => {
                    let srvc_type_id = singleEntry.srvc_type_id;
                    this.setModelKeyValue('srvc_type_id', srvc_type_id);
                    let resp = {};
                    resp['data'] = dbResp.data?.creation_resp?.[srvc_type_id];
                    const srvc_workflow = getServiceWorkflowModel(this);
                    srvc_workflow.trigger(singleEntry, 0, resp, 0, this.db);
                });
                resolve(
                    new sampleOperationResp(
                        true,
                        dbResp,
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }

    helperCreateAPIRespFrOrders(dbRespData) {
        let returnArray = [...dbRespData?.api_call_resp];
        return returnArray;
    }

    getAttachmentDataOfSubtask(query, entry_id, date) {
        return new Promise((resolve, reject) => {
            var requester = {};
            requester['org_id'] = users_model.getOrgId(this.userContext);
            //  requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['srvc_req_id'] = entry_id;
            requester['date'] = date;
            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var requesterInfo = JSON.stringify(requester);
            // console.log("requesterInfo",requesterInfo);
            dbObj.tms_get_attachment_of_sbtsks(requesterInfo).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }
                    var dbResp = new db_resp(
                        res[0].tms_get_attachment_of_sbtsks
                    );
                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    createOrUpdate(
        query,
        entry_id = 0,
        is_customer_access = 0,
        cust_org_id = 0,
        job_id = 0
    ) {
        //  console.log("Query rxd : ",JSON.stringify(query));
        console.log(
            'tms_create_service_request Query job_id',
            job_id,
            entry_id,
            JSON.stringify(query)
        );
        return new Promise(async (resolve, reject) => {
            // resolve(
            //     new sampleOperationResp(false,
            //         "Got Data - " + JSON.stringify(query),
            //         HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
            // );
            // return;
            // console.log("form_data",form_data);
            var validationResp = this.validateCreateNewForm(query);
            if (!validationResp.isSuccess()) {
                resolve(validationResp);
                return;
            }
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['srvc_type_id'] = this.srvcTypeId || query.srvc_type_id;
            query['is_customer_access'] = is_customer_access;

            let daily_status_updates = query['daily_status_updates'];
            let sp_daily_status_updates = query['sp_daily_status_updates'];
            if (daily_status_updates) {
                query['daily_status_updates'][query.updatedDay]['u_by'] =
                    users_model.getUUID(this.userContext);
            } else if (sp_daily_status_updates) {
                console.log(
                    'sp_daily_status_updates updatedDay',
                    query.updatedDay
                );
                query['sp_daily_status_updates'][query.updatedDay]['u_by'] =
                    users_model.getUUID(this.userContext);
            }

            if (
                entry_id > 0 &&
                is_customer_access == '1' &&
                !query?.isUpdateCityState
            ) {
                query['org_id'] = cust_org_id;
            }
            let pre_srvc_req_form_data;
            if (entry_id > 0) {
                pre_srvc_req_form_data =
                    await this.getSrvcReqFormDataFrAutomation({}, entry_id);
            }
            // console.log(this.srvcTypeId);
            // resolve(
            //     new sampleOperationResp(false,
            //         `Got Data - ${entry_id} - `  + JSON.stringify(query),
            //         HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR)
            // );
            // return;
            //If a user does not have approval access and is trying to change approval status and approval comments.
            if (
                query['discount_approval_status'] ||
                query['discount_approvers_comment']
            ) {
                query['srvc_req_id'] = entry_id;
                let hasAccess =
                    await this.doesLoginUserHasDisApprovalAccess(query);
                if (!hasAccess.hasDisApprovalAccess) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'You do not have access to approve this request.',
                            HttpStatus.StatusCodes.FORBIDDEN
                        )
                    );
                    return;
                }
            }

            let send_for_dis_approval_u_by = users_model.getUUID(
                this.userContext
            );
            if (query['send_for_discount_approval']) {
                query['send_for_discount_approval_u_by'] =
                    send_for_dis_approval_u_by;
            } else if (query['sp_send_for_discount_approval']) {
                query['sp_send_for_discount_approval_u_by'] =
                    send_for_dis_approval_u_by;
            }

            let send_for_billing = users_model.getUUID(this.userContext);
            if (query['send_for_billing']) {
                query['send_for_billing_u_by'] = send_for_billing;
            } else if (query['sp_send_for_billing']) {
                query['sp_send_for_billing_u_by'] = send_for_billing;
            }

            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            console.log(
                'tms_create_service_request Form_data job_id',
                job_id,
                form_data,
                entry_id
            );
            this.db.tms_create_service_request(form_data, entry_id).then(
                (res) => {
                    var dbResp = new db_resp(res[0].tms_create_service_request);

                    if (dbResp.code == 'title_or_key_exists') {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Title or Key not unique',
                                HttpStatus.StatusCodes.CONFLICT
                            )
                        );
                    } else if (dbResp.code == 'no_delete_access') {
                        resolve(
                            new sampleOperationResp(
                                true,
                                'No delete access',
                                HttpStatus.StatusCodes.OK
                            )
                        );
                        return;
                    } else if (dbResp.code == 'not_allowed_to_add_request') {
                        resolve(
                            new sampleOperationResp(
                                false,
                                ' Not allowed to add request',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                    } else if (
                        this.errorCodesToCheck()?.includes(dbResp.code)
                    ) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                dbResp.data,
                                HttpStatus.StatusCodes.BAD_REQUEST
                            )
                        );
                        return;
                    } else if (!dbResp.status) {
                        // console.log('Failed to update',dbResp.code)
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        let workflow = getServiceWorkflowModel(this);
                        query['pre_srvc_req_form_data'] =
                            pre_srvc_req_form_data;
                        workflow.trigger(
                            query,
                            entry_id,
                            dbResp,
                            is_customer_access,
                            this.db
                        );
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    console.log('in error');
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getViewDataFrForm(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            if (this.srvcTypeId != 0) {
                query['srvc_type_id'] = query.srvc_type_id || this.srvcTypeId;
            }
            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_srvc_getview_data(form_data).then(
                (res) => {
                    var srvcViewResp = new db_resp(
                        res[0].tms_srvc_getview_data
                    );
                    if (!srvcViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(srvcViewResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getViewDataFrSrvcTimelineForm(query) {
        return new Promise((resolve, reject) => {
            var { page_no_, page_size_, search_query, filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = {};
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['srvc_type_id'] = this.srvcTypeId;
            requester['srvc_req_id'] = this.srvcReqId;

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var requesterInfo = JSON.stringify(requester);

            // console.log("requesterInfo",requesterInfo);

            this.db
                .tms_get_srvc_timeline(
                    requesterInfo,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(res[0].tms_get_srvc_timeline);

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getAll(query, is_customer_access = 0) {
        return new Promise((resolve, reject) => {
            var { page_no_, page_size_, search_query, filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = {};
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['srvc_type_id'] = this.srvcTypeId;
            requester['is_customer_access'] = is_customer_access;
            requester['is_frontend'] = true;
            requester['retrieve_count'] = query.retrieve_count || false;

            let dbObj = this.db;
            // if (query.retrieve_count) {
            //     dbObj = this.dbReplica;
            // }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var requesterInfo = JSON.stringify(requester);
            // console.log("Call db function ",requesterInfo)
            dbObj
                .tms_get_srvc_reqs_v2(
                    requesterInfo,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(res[0].tms_get_srvc_reqs_v2);

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getCustHistory(query) {
        return new Promise((resolve, reject) => {
            var requester = {};
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['cust_mobile'] = query['cust_mobile'];

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var requesterInfo = JSON.stringify(requester);
            this.db.tms_get_cust_history(requesterInfo).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }

                    var dbResp = new db_resp(res[0].tms_get_cust_history);

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    errorCodesToCheck() {
        return [
            'invalid_srvc_prvdr_id',
            'invalid_mobile',
            'invalid_pincode',
            'invalid_pincode',
            'srvc_status_missing_fields',
        ];
    }

    validateCreateNewForm(form_data) {
        // if(form_data['srvc_type_name'] ==""
        //     || form_data['srvc_type_key'] ==""
        //     || form_data['srvc_type_icon_selector'] == "") {
        //     return new sampleOperationResp(false,
        //         "Mandatory parameters missing!",
        //         HttpStatus.StatusCodes.BAD_REQUEST);
        // }
        return new sampleOperationResp(
            true,
            'Good to go!',
            HttpStatus.StatusCodes.OK
        );
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    performProactiveRequestCreation(req) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            let proactiveLogger = ProactiveLogger;
            proactiveLogger.createNewProactiveCronObj(
                req.app.get('MACHINE_ID'),
                this.ip_address,
                this.user_agent_
            );
            this.db.tms_get_enabled_proactive_srvc_types().then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_enabled_proactive_srvc_types
                    );
                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        let proactiveSrvcTypes = dbResp.data;
                        proactiveLogger.cronJob['service_types_count'] =
                            proactiveSrvcTypes.length;
                        proactiveLogger.addCronAtES();
                        let resp = proactiveSrvcTypes.map(
                            (proactiveSrvcType) => {
                                return {
                                    srvc_type_name:
                                        proactiveSrvcType.srvc_type_name,
                                    srvc_type_id: proactiveSrvcType.id,
                                    org_id: proactiveSrvcType.form_data.org_id,
                                };
                            }
                        );

                        proactiveSrvcTypes.map(
                            //add a doc.scans in elastic
                            (singleProactiveServicetype) => {
                                allQueues.WIFY_TMS_PROACTIVE_QUEUE.addJob({
                                    proactiveLoggerFlat:
                                        proactiveLogger.flattenLogger(),
                                    singleProactiveServicetype,
                                    services_model_data:
                                        this.getServicesModelData(this),
                                });
                                // doProactiveRequestCreation(proactiveLogger,singleProactiveServicetype,this)
                            }
                        );

                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify({
                                    resp,
                                    cron: proactiveLogger.cronJob.id,
                                }),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getAllRequestsFrProactiveScanningAndSaveIntoJSON(
        proactiveLogger,
        singleProactiveServicetype
    ) {
        // wheenever a request needs to be created
        // we will need a system user for auto request creation
        // System user will be defined as a user to the
        // owner organization of TMS - (Which is WIFY)
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            let source_srvc_type_id =
                singleProactiveServicetype.form_data
                    .pro_active_source_srvc_type;
            this.db
                .tms_get_srvc_types_by_src_id_fr_proactive(
                    source_srvc_type_id,
                    { stream: true }
                )
                .then(
                    (stream) => {
                        const today_date = new Date(); // today, now
                        let today = today_date.toISOString().slice(0, 10);
                        let savePath = path.join(
                            '',
                            'temp_files',
                            'proactive_request_dump',
                            '' + today
                        );
                        fs.mkdir(savePath, { recursive: true }, async (err) => {
                            if (err) {
                                if (err.code != 'EEXIST') {
                                    return console.log(
                                        'Error in temp folder creation',
                                        err
                                    );
                                }
                            } else {
                                console.log('Directory created successfully!');
                            }
                            let fileName = `Service Requests dump ${today}_${today_date.getTime()}.json`;
                            let filePath = path.join(savePath, fileName);
                            stream.on('end', async () => {
                                console.log('Streaming ended -----');
                                await proactiveLogger.updateScanStreamEnded();
                                resolve({
                                    status: true,
                                    path: filePath,
                                    proactiveSrvcType:
                                        singleProactiveServicetype,
                                });
                            });
                            await proactiveLogger.updateScanStreamStarted();
                            stream
                                .pipe(JSONStream.stringify())
                                .pipe(
                                    JSONStream.parse(
                                        '*.tms_get_srvc_types_by_src_id_fr_proactive.*'
                                    )
                                )
                                .pipe(JSONStream.stringify())
                                .pipe(fs.createWriteStream(filePath));
                        });
                        //create a read stream and update a doc.scans.[id] in elastic
                        //proactive_srvc_type_id,proactive_srvc_details,
                        //download_from_database_complete,org_details,legacy_type_id,legacy_order_ids,count_legacy_orders,results
                    },
                    (err) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getViewDataFrBulkCreationForm(query) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);

            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_verticals_fr_bulk_creation(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_verticals_fr_bulk_creation
                    );
                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getViewDataFrVertical(query, entry_id) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['entry_id'] = entry_id;

            var form_data = JSON.stringify(query);

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_getview_data_fr_selected_vertical(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_getview_data_fr_selected_vertical
                    );
                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    createProactiveReqFrOldReq(
        proactiveLogger,
        old_req,
        proactive_srvc_type_id,
        proactive_ruleset_json,
        proactiveSrvcType
    ) {
        // ONCE YOU ARE BACK DO BELOW 2 LINES
        // 1. Save the ruleset that triggered this request creation inside form data->'proactive_ruleset_json'
        // 1. Save the old request id inside form data->'proactive_old_req_id'

        // 1. create new service request in proactive calls - with basic form data(Mob,Name,Addrr,Description - Re - <old_Desc>,Normal,req_date will be today) from old req,
        // 2. In legacy order update a field called 'latest_proactive_req_c_date' and 'latest_proactive_req_id'
        // 3. Add a comment in legacy order saying 'Proactive request created with ID - <display title>

        return new Promise((resolve, reject) => {
            let org_id = old_req.org_id;
            let query = {};
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['org_id'] = org_id;

            var form_data = JSON.stringify(query);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_or_create_system_usr(form_data).then(
                async (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_or_create_system_usr
                    );

                    if (!dbResp.status) {
                        // console.log('Failed to update',dbResp.code)
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );

                        return;
                    } else {
                        var respData = dbResp.data;
                        let usr_id = respData['usr_id'];

                        //make a form_data for create srvc req
                        query['usr_id'] = usr_id;
                        query['usr_id'] = usr_id;
                        query['srvc_type_id'] = proactive_srvc_type_id;
                        query['old_req'] = old_req;

                        let dummy_req_for_user_context = {
                            uuid: usr_id,
                            user_details: {
                                org: {
                                    id: org_id,
                                },
                            },
                        };

                        this.user_context = getUserContextFrmReq(
                            dummy_req_for_user_context
                        );
                        this.userContext = getUserContextFrmReq(
                            dummy_req_for_user_context
                        );
                        this.srvc_type_id = proactive_srvc_type_id;

                        let finalQuery = {
                            ...query,
                            ...this.getSrvcReqBasicDetails(old_req),
                            proactive_old_req_id: old_req.id,
                            proactive_ruleset_json,
                        };

                        const lambdaARN =
                            proactiveSrvcType?.form_data?.proactive_lambda_arn;

                        if (lambdaARN) {
                            const payload = {
                                ...old_req,
                                proactiveSrvcType,
                            };

                            //Call lambda function here
                            const params = {
                                FunctionName: lambdaARN,
                                InvocationType: 'RequestResponse',
                                LogType: 'Tail',
                                Payload: JSON.stringify(payload),
                            };

                            try {
                                const respData = await callLambdaFn(params);
                                const lambdaRespData = JSON.parse(
                                    respData.Payload
                                );

                                if (lambdaRespData && lambdaRespData.status) {
                                    finalQuery = {
                                        ...finalQuery,
                                        ...lambdaRespData?.data,
                                    };
                                } else {
                                    resolve(
                                        new sampleOperationResp(
                                            false,
                                            lambdaRespData.message,
                                            HttpStatus.StatusCodes.BAD_REQUEST
                                        )
                                    );
                                    return;
                                }
                            } catch (error) {
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'Could not process, please contact admin.',
                                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                    )
                                );
                                return;
                            }
                        }
                        var final_form_data = JSON.stringify(finalQuery);
                        await proactiveLogger.updateScanResult({
                            new_request_form_data: finalQuery,
                        });
                        console.log(
                            'tms_create_proactive_service_request  final_form_data ',
                            final_form_data
                        );
                        this.db
                            .tms_create_proactive_service_request_v1(
                                final_form_data
                            )
                            .then(
                                (res) => {
                                    console.log(
                                        'tms_create_proactive_service_request',
                                        res
                                    );
                                    var dbResp = new db_resp(
                                        res[0].tms_create_proactive_service_request_v1
                                    );

                                    if (dbResp.code == 'title_or_key_exists') {
                                        resolve(
                                            new sampleOperationResp(
                                                false,
                                                'Title or Key not unique',
                                                HttpStatus.StatusCodes.CONFLICT
                                            )
                                        );
                                    } else if (
                                        dbResp.code ==
                                        'not_allowed_to_add_request'
                                    ) {
                                        resolve(
                                            new sampleOperationResp(
                                                false,
                                                ' Not allowed to add request',
                                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                            )
                                        );
                                    } else if (!dbResp.status) {
                                        // console.log('Failed to update',dbResp.code)
                                        resolve(
                                            new sampleOperationResp(
                                                false,
                                                'Internal server Error',
                                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                            )
                                        );

                                        return;
                                    } else {
                                        let workflow =
                                            getServiceWorkflowModel(this);
                                        workflow.trigger(
                                            finalQuery,
                                            0,
                                            dbResp.data,
                                            false,
                                            this.db
                                        );
                                        resolve(
                                            new sampleOperationResp(
                                                true,
                                                JSON.stringify(dbResp.data),
                                                HttpStatus.StatusCodes.OK
                                            )
                                        );
                                    }
                                },
                                (error) => {
                                    this.fatalDbError(resolve, error);
                                }
                            );
                    }
                },
                (error) => {
                    console.log(
                        'tms_create_proactive_service_request error',
                        error
                    );
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getProjectOverviewProto(query) {
        return new Promise((resolve, reject) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            if (this.vertical_id) {
                query['vertical_id'] = this.vertical_id;
            }
            var form_data = JSON.stringify(query);
            console.log('form_data', form_data);
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_get_project_access_overview_proto(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_project_access_overview_proto
                    );
                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getProjectSrvcReqListByVerticalId(query) {
        return new Promise((resolve, reject) => {
            var { page_no_, page_size_, search_query, filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = {};
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['vertical_id'] = this.vertical_id;
            requester['verticals_list'] = [this.vertical_id];
            requester['srvc_type_id'] = this.srvcTypeId || 0;
            requester['is_customer_access'] = 0;
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var requesterInfo = JSON.stringify(requester);
            console.log('requesterInfo', requesterInfo);
            this.db
                .tms_get_projects_details(
                    requesterInfo,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(
                            res[0].tms_get_projects_details
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    async autoAssignAuthoritiesToSrvcReq(query) {
        query['pagination'] = '{}'; // dummy
        var { filters_ } = pagination_filters_utils.decodeQueryParams(query);

        query['org_id'] = users_model.getOrgId(this.userContext);
        query['usr_id'] = users_model.getUUID(this.userContext);
        query['ip_addr'] = this.ip_address;
        query['user_agent'] = this.user_agent_;
        const jobData = {
            query,
            services_model_data: await this.getServicesModelData(this),
        };
        allQueues.WIFY_AUTO_ASSIGN_AUTORITY_GET_SRVC_REQ_OF_SRVC_TYPE.addJob(
            jobData
        );

        return new sampleOperationResp(
            true,
            'success',
            HttpStatus.StatusCodes.OK
        );
    }
    processAutoAssignAuthorityByLocOnSrvcReq(jobData) {
        return new Promise((resolve, reject) => {
            try {
                let is_apply_to_all_active_req =
                    jobData?.query?.is_apply_to_all_active_req;
                let requesterInfo = JSON.stringify(jobData.query);
                const dbObj = this.dbDump || this.dbReplica || this.db;
                if (this.dbDump) {
                    console.log('Loading data from Dump');
                } else if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }
                let srvc_type_id_ = jobData.query.srvc_type_id;
                dbObj
                    .tms_get_all_srvc_req_by_srvc_type_id_fr_auto_assign_authority(
                        srvc_type_id_,
                        is_apply_to_all_active_req
                    )
                    .then(
                        (res) => {
                            const srvcReqIds =
                                res[0]
                                    .tms_get_all_srvc_req_by_srvc_type_id_fr_auto_assign_authority;
                            srvcReqIds.forEach((singleId) => {
                                const jobData = {
                                    singleId,
                                    requesterInfo,
                                    services_model_data:
                                        this.getServicesModelData(this),
                                };
                                allQueues.WIFY_AUTO_ASSIGN_AUTHORITY_ON_EXISTING_SRVC_REQS.addJob(
                                    jobData
                                );
                            });
                        },
                        (error) => {
                            this.fatalDbError(resolve, error);
                        }
                    );
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }

    async processSendMailBasedOnTimeConfig(srvcTypeVsNotificationReminder) {
        try {
            const srvcTypeId = srvcTypeVsNotificationReminder.srvcTypeId;
            const orgId = srvcTypeVsNotificationReminder.orgId;
            let srvc_type_time_based_wise_notification =
                srvcTypeVsNotificationReminder.singleNotificationConfig;
            const primaryReceipantRoleIds =
                srvc_type_time_based_wise_notification.select_primary_recipient;
            const static_cc_users =
                srvc_type_time_based_wise_notification.static_cc_users;
            const static_cc_users_uuids = Array.isArray(static_cc_users)
                ? static_cc_users.map((item) => item.value)
                : [];

            let query = {};
            query['org_id'] = orgId;
            query['srvc_type_id'] = srvcTypeId;
            query['primary_receipant_role_ids'] = primaryReceipantRoleIds;
            query['static_cc_users_uuids'] = static_cc_users_uuids;
            query['filters'] = {
                creation_srvc_req_date: get5MonthsDateRangeFilter(),
            };
            let form_data = JSON.stringify(query);
            console.log('form_data', form_data);
            let stream = await this.db.tms_get_time_based_reminders(form_data, {
                stream: true,
            });
            const today_date = new Date(); // today, now
            let today = today_date.toISOString().slice(0, 10);
            let savePath = path.join(
                '',
                'temp_files',
                'time_based_reminders',
                '' + today
            );
            fs.mkdir(savePath, { recursive: true }, async (err) => {
                if (err) {
                    if (err.code != 'EEXIST') {
                        return console.log(
                            'Error in temp folder creation',
                            err
                        );
                    }
                } else {
                    console.log('Directory created successfully!');
                }
                let fileName = `SrvcType Wise Time Based Config reminder data ${today}_${today_date.getTime()}.json`;
                let filePath = path.join(savePath, fileName);
                stream.on('end', async () => {
                    console.log('Streaming ended -----');
                    allQueues.WIFY_TMS_PROCESS_READ_TIME_BASED_NOTIFICATION_REMINDERS.addJob(
                        {
                            srvcTypeVsNotificationReminder,
                            filePath,
                        }
                    );
                    return;
                });
                stream
                    .pipe(JSONStream.stringify())
                    .pipe(JSONStream.parse('*.tms_get_time_based_reminders.*'))
                    .pipe(JSONStream.stringify())
                    .pipe(fs.createWriteStream(filePath));
            });
        } catch (error) {
            console.error('Error:', error);
            this.fatalDbError(error);
            return;
        }
    }

    getSrvcReqBasicDetails(old_req) {
        let old_req_form_data = old_req.form_data;
        let basic_details = {};
        let basic_details_keys = [
            'cust_mobile',
            'cust_full_name',
            'cust_line_0',
            'cust_line_1',
            'cust_line_2',
            'cust_line_3',
            'cust_pincode',
            'cust_city',
            'cust_state',
            'cust_email',
        ];
        basic_details_keys.map((basic_field_key) => {
            basic_details[basic_field_key] = old_req_form_data[basic_field_key];
        });
        basic_details['request_description'] =
            'Re - ' + old_req_form_data.request_description;
        basic_details['request_priority'] = 'Normal';
        return basic_details;
    }

    getViewDataFrSrvcTransitionOverviewForm(query) {
        return new Promise((resolve, reject) => {
            try {
                const { page_no_, page_size_, search_query, filters_ } =
                    pagination_filters_utils.decodeQueryParams(query);
                let requester = {};
                requester['org_id'] = users_model.getOrgId(this.userContext);
                requester['usr_id'] = users_model.getUUID(this.userContext);
                requester['ip_address'] = this.ip_address;
                requester['user_agent'] = this.user_agent_;
                requester['srvc_type_id'] = this.srvcTypeId;
                requester['srvc_req_id'] = this.srvcReqId;
                let requesterInfo = JSON.stringify(requester);
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                // console.log('requesterInfo',requesterInfo);

                this.db
                    .tms_get_transition_overview_fr_srvc_req(
                        requesterInfo,
                        page_no_,
                        page_size_,
                        filters_,
                        search_query
                    )
                    .then(
                        (res) => {
                            if (!res || !res[0]) {
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'Unknown error',
                                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                    )
                                );
                                return;
                            }

                            var dbResp = new db_resp(
                                res[0].tms_get_transition_overview_fr_srvc_req
                            );

                            if (!dbResp.status) {
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'Internal server Error',
                                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                    )
                                );

                                return;
                            } else {
                                resolve(
                                    new sampleOperationResp(
                                        true,
                                        JSON.stringify(dbResp.data),
                                        HttpStatus.StatusCodes.OK
                                    )
                                );
                                return;
                            }
                        },
                        (error) => {
                            this.fatalDbError(resolve, error);
                        }
                    );
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }

    getGlobalSearchResults(query) {
        return new Promise((resolve, reject) => {
            var { search_query } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = {};
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;

            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var requesterInfo = JSON.stringify(requester);
            this.db
                .tms_get_global_search_results(requesterInfo, search_query)
                .then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        var dbResp = new db_resp(
                            res[0].tms_get_global_search_results
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }
    getSubtaskDetailsForAutoStatus(query, entry_id = 0) {
        return new Promise((resolve, reject) => {
            try {
                query['org_id'] = users_model.getOrgId(this.userContext);
                query['usr_id'] = users_model.getUUID(this.userContext);
                query['ip_address'] = this.ip_address;
                query['user_agent'] = this.user_agent_;
                query['srvc_type_id'] = query.srvc_type_id || this.srvcTypeId;
                query['entry_id'] = entry_id;

                const form_data = JSON.stringify(query);

                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                this.db.tms_get_sbtsk_details_fr_auto_status(form_data).then(
                    (res) => {
                        if (!res || !res[0]) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Unknown error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        }

                        const dbResp = new db_resp(
                            res[0].tms_get_sbtsk_details_fr_auto_status
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );

                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }
    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set databaseReplica(db) {
        this.dbReplica = db;
    }

    get databaseReplica() {
        return this.dbReplica;
    }

    set databaseDump(db) {
        this.dbDump = db;
    }

    get databaseDump() {
        return this.dbDump;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    set srvc_type_id(srvcTypeId) {
        this.srvcTypeId = srvcTypeId;
    }

    get srvc_type_id() {
        return this.srvcTypeId;
    }

    set srvc_req_id(srvcReqId) {
        this.srvcReqId = srvcReqId;
    }

    get srvc_req_id() {
        return this.srvcReqId;
    }

    set vertical_id(verticalId) {
        this.verticalId = verticalId;
    }

    get vertical_id() {
        return this.verticalId;
    }

    setModelKeyValue(key, value) {
        this[key] = value;
    }

    getServicesModelData(services_model, getUserAndOrgIdFrQuery = false) {
        let userAndOrgIdFrQuery = {};
        if (getUserAndOrgIdFrQuery) {
            userAndOrgIdFrQuery = {
                org_id: users_model.getOrgId(this.userContext),
                usr_id: users_model.getUUID(this.userContext),
            };
        }
        return {
            ip_address: services_model.ip_address,
            user_agent: services_model.user_agent_,
            user_context: services_model.user_context,
            srvc_type_id: services_model.srvc_type_id,
            srvc_req_id: services_model.srvc_req_id,
            ...userAndOrgIdFrQuery,
        };
    }

    getFreshInstance(model) {
        const clonedInstance = new services_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }

    getInstance() {
        const instance = new services_model();
        return instance;
    }
}

module.exports = new services_model();
