var sampleOperationResp = require('./utils/operationResp');
var HttpStatus = require('http-status-codes');
const db_resp = require('./utils/db_resp');
const { allQueues } = require('./queues_v2/queues');
const users_model = require('./users_model');

class bulk_update_model {
    /**
     * Update status for multiple service requests in batch
     * @param {Array} batchData - Array of service requests with new status information
     * @returns {Promise<Object>} - Promise resolving to operation result
     */
    async updateServiceRequestStatusBatch(batchData) {
        return new Promise(async (resolve, reject) => {
            try {
                if (
                    !batchData ||
                    !Array.isArray(batchData) ||
                    batchData.length === 0
                ) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Invalid batch data. Expected non-empty array.',
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                    return;
                }

                // Process in batches of 100
                const batchSize = 100;
                const totalRecords = batchData.length;
                const totalBatches = Math.ceil(totalRecords / batchSize);
                let jobsAdded = 0;

                // get current loggedin user email
                for (
                    let batchNumber = 0;
                    batchNumber < totalBatches;
                    batchNumber++
                ) {
                    const startIdx = batchNumber * batchSize;
                    const endIdx = Math.min(
                        startIdx + batchSize,
                        batchData.length
                    );
                    const batchSlice = batchData.slice(startIdx, endIdx);

                    const jobData = {
                        query: {
                            ip_address: this.ip_address,
                            user_agent: this.user_agent_,
                            limit: batchSize,
                            batch_data: batchSlice,
                        },
                        bulk_update_model_data: this.getModelDataForQueue(),
                        batchSize,
                        batchNumber: batchNumber + 1,
                        totalBatches,
                        totalRecords,
                    };

                    // Add job to the queue
                    allQueues.WIFY_BULK_UPDATE_SRVC_REQS_STATUS.addJob(
                        jobData,
                        {
                            attempts: 10,
                            backoff: {
                                type: 'exponential',
                                delay: 5000,
                            },
                            removeOnComplete: true,
                            removeOnFail: false,
                        }
                    );

                    jobsAdded++;
                }

                resolve(
                    new sampleOperationResp(
                        true,
                        JSON.stringify({
                            message:
                                'Started updating status for service requests',
                            totalRecords: batchData.length,
                            totalJobsAdded: jobsAdded,
                        }),
                        HttpStatus.StatusCodes.OK
                    )
                );
            } catch (error) {
                console.error(
                    'Error in updateServiceRequestStatusBatch:',
                    error
                );
                resolve(
                    new sampleOperationResp(
                        false,
                        error.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    /**
     * Process a batch of service request status updates
     * @param {Object} query - Query object containing batch data and metadata
     * @returns {Promise<Object>} - Promise resolving to operation result with updated counts and details
     */
    async processUpdateSrvcReqStatusBatch(query) {
        return new Promise(async (resolve, reject) => {
            try {
                if (!this.db) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }
                const { batch_data } = query;
                if (
                    !batch_data ||
                    !Array.isArray(batch_data) ||
                    batch_data.length === 0
                ) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'Invalid batch data. Expected non-empty array.',
                            HttpStatus.StatusCodes.BAD_REQUEST
                        )
                    );
                    return;
                }
                // console.log(
                //     'processUpdateSrvcReqStatusBatch :: query :: ',
                //     JSON.stringify(query)
                // );

                let respData = (
                    await this.db.tms_owner_update_srvc_req_without_workflow(
                        JSON.stringify(query)
                    )
                )[0].tms_owner_update_srvc_req_without_workflow;

                if (!respData.status) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            respData?.message || 'Internal server error',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                } else {
                    resolve(
                        new sampleOperationResp(
                            true,
                            JSON.stringify(respData.data),
                            HttpStatus.StatusCodes.OK
                        )
                    );
                }
            } catch (error) {
                console.log('processUpdateSrvcReqStatusBatch :: error ', error);
                resolve(
                    new sampleOperationResp(
                        false,
                        error?.message || 'Internal server error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getModelDataForQueue() {
        return {
            ip_addr: this.ip_address,
            user_agent: this.user_agent_,
            user_context: this.user_context,
        };
    }

    getFreshInstance(model) {
        const clonedInstance = new bulk_update_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }

    getInstance() {
        const instance = new bulk_update_model();
        return instance;
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }
    get user_context() {
        return this.userContext;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }
}

module.exports = new bulk_update_model();
